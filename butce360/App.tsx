/**
 * Butce360 - Personal Finance Management App
 * Pure React Native Implementation
 */

import React, { useEffect } from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { NavigationProvider } from './src/navigation/AppNavigator';
import { AuthProvider } from './src/context/AuthContext';
import AppNavigator from './src/AppNavigator';
// import { googleSignInService } from './src/services/googleSignInService';

function App() {
  useEffect(() => {
    // Configure Google Sign-In on app start
    // TODO: Enable after Google Cloud Console setup
    // googleSignInService.configure();
  }, []);

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <AuthProvider>
        <NavigationProvider initialScreen="Welcome">
          <AppNavigator />
        </NavigationProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}

export default App;
