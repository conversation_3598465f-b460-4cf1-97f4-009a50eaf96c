// Simple storage service using React Native's built-in storage capabilities
// This is a pure React Native implementation without external dependencies

// interface StorageItem { // Not used
  // key: string;
  // value: string;
// }

class StorageService {
  private storage: Map<string, string> = new Map();
  private initialized = false;

  // Initialize storage (in a real app, this would load from device storage)
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // In a real implementation, you would load from device storage here
      // For now, we'll use in-memory storage
      this.initialized = true;
      console.log('[Storage] Initialized');
    } catch (error) {
      console.error('[Storage] Initialization failed:', error);
      throw error;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    await this.initialize();
    this.storage.set(key, value);
    console.log(`[Storage] Set item: ${key}`);
  }

  async getItem(key: string): Promise<string | null> {
    await this.initialize();
    const value = this.storage.get(key) || null;
    console.log(`[Storage] Get item: ${key} = ${value ? 'found' : 'not found'}`);
    return value;
  }

  async removeItem(key: string): Promise<void> {
    await this.initialize();
    this.storage.delete(key);
    console.log(`[Storage] Removed item: ${key}`);
  }

  async multiRemove(keys: string[]): Promise<void> {
    await this.initialize();
    keys.forEach(key => {
      this.storage.delete(key);
    });
    console.log(`[Storage] Removed items: ${keys.join(', ')}`);
  }

  async clear(): Promise<void> {
    await this.initialize();
    this.storage.clear();
    console.log('[Storage] Cleared all items');
  }

  async getAllKeys(): Promise<string[]> {
    await this.initialize();
    return Array.from(this.storage.keys());
  }

  // Utility methods for JSON data
  async setObject<T>(key: string, value: T): Promise<void> {
    await this.setItem(key, JSON.stringify(value));
  }

  async getObject<T>(key: string): Promise<T | null> {
    const value = await this.getItem(key);
    if (!value) return null;

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error(`[Storage] Failed to parse JSON for key ${key}:`, error);
      return null;
    }
  }
}

// Export singleton instance
export const storage = new StorageService();

// Storage keys constants
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  AUTH_USER: 'auth_user',
  USER_PREFERENCES: 'user_preferences',
  CACHED_CATEGORIES: 'cached_categories',
  CACHED_ACCOUNTS: 'cached_accounts',
  GUEST_MODE: 'guest_mode',
} as const;