import { NativeModules, Platform } from 'react-native';

interface GoogleSignInResult {
  success: boolean;
  userID?: string;
  email?: string;
  name?: string;
  givenName?: string;
  familyName?: string;
  imageUrl?: string;
  idToken?: string;
  accessToken?: string;
  error?: string;
}

interface GoogleSignInModule {
  configure(): Promise<{ success: boolean }>;
  signIn(): Promise<GoogleSignInResult>;
  signOut(): Promise<{ success: boolean }>;
  isSignedIn(): Promise<{ isSignedIn: boolean }>;
}

class NativeGoogleSignInService {
  private googleSignIn: GoogleSignInModule | null = null;
  private isConfigured = false;

  constructor() {
    if (Platform.OS === 'ios') {
      this.googleSignIn = NativeModules.GoogleSignInManager;
    }
  }

  async configure(): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.googleSignIn) {
      console.log('[NativeGoogleSignInService] Google Sign-In not available on this platform');
      return false;
    }

    if (this.isConfigured) {
      return true;
    }

    try {
      console.log('[NativeGoogleSignInService] Configuring Google Sign-In...');
      const result = await this.googleSignIn.configure();
      this.isConfigured = result.success;
      console.log('[NativeGoogleSignInService] Configuration result:', result);
      return this.isConfigured;
    } catch (error) {
      console.error('[NativeGoogleSignInService] Configuration error:', error);
      return false;
    }
  }

  async signIn(): Promise<GoogleSignInResult> {
    if (Platform.OS !== 'ios' || !this.googleSignIn) {
      return {
        success: false,
        error: 'Google Sign-In is only available on iOS'
      };
    }

    // Ensure configuration
    if (!this.isConfigured) {
      const configured = await this.configure();
      if (!configured) {
        return {
          success: false,
          error: 'Failed to configure Google Sign-In'
        };
      }
    }

    try {
      console.log('[NativeGoogleSignInService] Starting Google Sign-In...');
      const result = await this.googleSignIn.signIn();
      console.log('[NativeGoogleSignInService] Google Sign-In result:', result);
      
      return {
        ...result,
        success: true
      };
    } catch (error: any) {
      console.error('[NativeGoogleSignInService] Google Sign-In error:', error);
      
      return {
        success: false,
        error: error.message || 'Google Sign-In failed'
      };
    }
  }

  async signOut(): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.googleSignIn) {
      return false;
    }

    try {
      const result = await this.googleSignIn.signOut();
      return result.success;
    } catch (error) {
      console.error('[NativeGoogleSignInService] Sign-out error:', error);
      return false;
    }
  }

  async isSignedIn(): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.googleSignIn) {
      return false;
    }

    try {
      const result = await this.googleSignIn.isSignedIn();
      return result.isSignedIn;
    } catch (error) {
      console.error('[NativeGoogleSignInService] Error checking sign-in status:', error);
      return false;
    }
  }

  // Format user data for our app
  formatUserData(result: GoogleSignInResult) {
    if (!result.success) {
      return null;
    }

    return {
      id: result.userID,
      email: result.email || '',
      name: result.name || 'Google User',
      givenName: result.givenName,
      familyName: result.familyName,
      imageUrl: result.imageUrl,
      provider: 'google',
      idToken: result.idToken,
      accessToken: result.accessToken,
    };
  }
}

export const nativeGoogleSignInService = new NativeGoogleSignInService();
