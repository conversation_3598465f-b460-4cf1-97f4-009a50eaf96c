import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Logo from '../../components/common/Logo';

const WelcomePage: React.FC = () => {
  const { navigate } = useNavigation();
  const { enableGuestMode } = useAuth();

  const handleLogin = () => {
    navigate('Login');
  };

  const handleRegister = () => {
    navigate('Register');
  };

  const handleGuestMode = async () => {
    try {
      await enableGuestMode();
      navigate('MainTabs');
    } catch (error) {
      console.error('Error enabling guest mode:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />

      <View style={styles.content}>
        {/* Logo and Title */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Logo size={120} variant="rounded" />
          </View>
          <Text style={styles.title}>Butce360</Text>
          <Text style={styles.subtitle}>
            Gelir ve giderlerinizi kolayca takip edin
          </Text>
        </View>

        {/* Features */}
        <View style={styles.features}>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>📊</Text>
            <Text style={styles.featureText}>Detaylı raporlar</Text>
          </View>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>🎯</Text>
            <Text style={styles.featureText}>Bütçe hedefleri</Text>
          </View>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>📱</Text>
            <Text style={styles.featureText}>Kolay kullanım</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actions}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleLogin}>
            <Text style={styles.primaryButtonText}>Giriş Yap</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={handleRegister}>
            <Text style={styles.secondaryButtonText}>Hesap Oluştur</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.guestButton} onPress={handleGuestMode}>
            <Text style={styles.guestButtonText}>Misafir Olarak Devam Et</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.screenPadding,
    justifyContent: 'space-between',
    paddingTop: spacing['6xl'],
    paddingBottom: spacing['4xl'],
  },
  header: {
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['2xl'],
  },
  title: {
    ...typography.styles.h1,
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 280,
  },
  features: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: spacing.lg,
  },
  feature: {
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    fontSize: 32,
    marginBottom: spacing.sm,
  },
  featureText: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  actions: {
    gap: spacing.lg,
  },
  primaryButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
  },
  primaryButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },
  secondaryButton: {
    backgroundColor: colors.surface.primary,
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  secondaryButtonText: {
    ...typography.styles.button,
    color: colors.primary[500],
    textTransform: 'none',
  },
  guestButton: {
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  guestButtonText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textDecorationLine: 'underline',
  },
});

export default WelcomePage;
