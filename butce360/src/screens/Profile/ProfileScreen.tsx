import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Logo from '../../components/common/Logo';

interface MenuItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress: () => void;
  showArrow?: boolean;
  danger?: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({ 
  icon, 
  title, 
  subtitle, 
  onPress, 
  showArrow = true, 
  danger = false 
}) => (
  <TouchableOpacity style={styles.menuItem} onPress={onPress}>
    <View style={styles.menuItemLeft}>
      <Text style={styles.menuItemIcon}>{icon}</Text>
      <View style={styles.menuItemInfo}>
        <Text style={[
          styles.menuItemTitle,
          danger && styles.dangerText
        ]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={styles.menuItemSubtitle}>{subtitle}</Text>
        )}
      </View>
    </View>
    {showArrow && (
      <Text style={styles.menuItemArrow}>›</Text>
    )}
  </TouchableOpacity>
);

const ProfileScreen: React.FC = () => {
  const { navigate, goBack } = useNavigation();
  const { state: authState, logout, disableGuestMode } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Çıkış Yap', 
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              navigate('Welcome');
            } catch (error) {
              Alert.alert('Hata', 'Çıkış yapılırken bir hata oluştu.');
            }
          }
        },
      ]
    );
  };

  const handleGuestLogin = () => {
    Alert.alert(
      'Giriş Yap',
      'Tüm özelliklerden yararlanmak için giriş yapmak istiyor musunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Giriş Yap', 
          onPress: async () => {
            try {
              await disableGuestMode();
              navigate('Login');
            } catch (error) {
              console.error('Error disabling guest mode:', error);
            }
          }
        },
      ]
    );
  };

  const renderUserInfo = () => {
    if (authState.isGuest) {
      return (
        <View style={styles.userCard}>
          <View style={styles.avatarContainer}>
            <Logo size={60} variant="circle" />
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>Misafir Kullanıcı</Text>
            <Text style={styles.userEmail}>Giriş yapmadınız</Text>
          </View>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleGuestLogin}
          >
            <Text style={styles.loginButtonText}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.userCard}>
        <View style={styles.avatarContainer}>
          <Text style={styles.avatarText}>
            {authState.user?.name?.charAt(0).toUpperCase() || '👤'}
          </Text>
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            {authState.user?.name || 'Kullanıcı'}
          </Text>
          <Text style={styles.userEmail}>
            {authState.user?.email || '<EMAIL>'}
          </Text>
        </View>
        <TouchableOpacity 
          style={styles.editButton}
          onPress={() => navigate('EditProfile')}
        >
          <Text style={styles.editButtonText}>Düzenle</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderAccountSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Hesap</Text>
      <View style={styles.menuGroup}>
        <MenuItem
          icon="👤"
          title="Profil Bilgileri"
          subtitle="Ad, e-posta ve diğer bilgiler"
          onPress={() => {
            if (authState.isGuest) {
              handleGuestLogin();
            } else {
              navigate('EditProfile');
            }
          }}
        />
        <MenuItem
          icon="🔒"
          title="Şifre Değiştir"
          subtitle="Hesap güvenliğinizi koruyun"
          onPress={() => {
            if (authState.isGuest) {
              handleGuestLogin();
            } else {
              navigate('ChangePassword');
            }
          }}
        />
      </View>
    </View>
  );

  const renderDataSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Veriler</Text>
      <View style={styles.menuGroup}>
        <MenuItem
          icon="📂"
          title="Kategoriler"
          subtitle="Gelir ve gider kategorilerinizi yönetin"
          onPress={() => navigate('Categories')}
        />
        <MenuItem
          icon="🏦"
          title="Hesaplar"
          subtitle="Banka hesapları ve kartlarınız"
          onPress={() => navigate('Accounts')}
        />
        <MenuItem
          icon="📊"
          title="Raporlar"
          subtitle="Detaylı analiz ve istatistikler"
          onPress={() => navigate('Reports')}
        />
      </View>
    </View>
  );

  const renderAppSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Uygulama</Text>
      <View style={styles.menuGroup}>
        <MenuItem
          icon="⚙️"
          title="Ayarlar"
          subtitle="Uygulama tercihleri"
          onPress={() => navigate('Settings')}
        />
        <MenuItem
          icon="❓"
          title="Yardım"
          subtitle="SSS ve destek"
          onPress={() => navigate('Help')}
        />
        <MenuItem
          icon="ℹ️"
          title="Hakkında"
          subtitle="Uygulama bilgileri"
          onPress={() => navigate('About')}
        />
      </View>
    </View>
  );

  const renderLogoutSection = () => (
    <View style={styles.section}>
      <View style={styles.menuGroup}>
        <MenuItem
          icon="🚪"
          title={authState.isGuest ? "Misafir Modundan Çık" : "Çıkış Yap"}
          onPress={handleLogout}
          showArrow={false}
          danger={true}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Profil</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderUserInfo()}
        {renderAccountSection()}
        {renderDataSection()}
        {renderAppSection()}
        {renderLogoutSection()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text.primary,
  },
  title: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  headerRight: {
    width: 40,
  },

  // Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing['4xl'],
  },

  // User Card
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface.primary,
    marginHorizontal: spacing.screenPadding,
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarContainer: {
    marginRight: spacing.lg,
  },
  avatarText: {
    fontSize: 24,
    color: colors.primary[600],
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  userEmail: {
    ...typography.styles.body2,
    color: colors.text.secondary,
  },
  loginButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    borderRadius: spacing.cardRadius,
  },
  loginButtonText: {
    ...typography.styles.buttonSmall,
    color: colors.text.inverse,
    textTransform: 'none',
  },
  editButton: {
    backgroundColor: colors.background.secondary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    borderRadius: spacing.cardRadius,
  },
  editButtonText: {
    ...typography.styles.buttonSmall,
    color: colors.text.primary,
    textTransform: 'none',
  },

  // Sections
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.styles.h6,
    color: colors.text.primary,
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.md,
  },
  menuGroup: {
    backgroundColor: colors.surface.primary,
    marginHorizontal: spacing.screenPadding,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },

  // Menu Items
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemIcon: {
    fontSize: 20,
    marginRight: spacing.md,
    width: 24,
    textAlign: 'center',
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemTitle: {
    ...typography.styles.body1,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  menuItemSubtitle: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  menuItemArrow: {
    fontSize: 18,
    color: colors.text.secondary,
  },
  dangerText: {
    color: colors.error[500],
  },
});

export default ProfileScreen;
