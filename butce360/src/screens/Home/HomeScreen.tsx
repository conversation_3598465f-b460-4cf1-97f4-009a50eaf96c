import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  // StyleSheet, // Not used
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { NumberFormatter } from '../../utils/number';
import { DateFormatter } from '../../utils/date';
import { styles as homeStyles } from './styles';
import Logo from '../../components/common/Logo';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { transactionService } from '../../services/transactionService';
import { reportService, ReportSummary } from '../../services/reportService';
import { Transaction } from '../../types/models';

// Removed mock data - now using real API data

const HomeScreen: React.FC = () => {
  const { navigate } = useNavigation();
  const { state: authState } = useAuth();

  // State for API data
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [summary, setSummary] = useState<ReportSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  // Fetch data from API
  const fetchData = useCallback(async () => {
    try {
      setError(null);

      // Only fetch data if user is authenticated (not guest)
      if (authState.isAuthenticated && !authState.isGuest) {
        // Fetch data with individual error handling
        let recentTransactions: Transaction[] = [];
        let reportSummary: ReportSummary | null = null;

        try {
          recentTransactions = await transactionService.getRecentTransactions(5);
        } catch (error) {
          console.error('[HomeScreen] Error fetching transactions:', error);
        }

        try {
          reportSummary = await reportService.getSummary(selectedPeriod);
        } catch (error) {
          console.error('[HomeScreen] Error fetching summary:', error);
        }

        setTransactions(recentTransactions || []);
        setSummary(reportSummary || null);
      } else {
        // For guest users, use mock data or empty state
        setTransactions([]);
        setSummary(null);
      }
    } catch (error) {
      console.error('[HomeScreen] Error fetching data:', error);
      setError(error instanceof Error ? error.message : 'Veri yüklenirken hata oluştu');

      // Fallback to empty data on error
      setTransactions([]);
      setSummary(null);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [authState.isAuthenticated, authState.isGuest, selectedPeriod]);

  // Refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchData();
  };

  // Load data on component mount and auth state change
  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authState.isAuthenticated, authState.isGuest, selectedPeriod]);

  // Note: fetchData is not included in dependency array to prevent infinite loop

  const handleAddTransaction = (type: 'income' | 'expense') => {
    if (authState.isGuest) {
      // Show guest mode alert
      Alert.alert('Giriş Gerekli', 'Bu özelliği kullanmak için giriş yapmanız gerekiyor.');
      return;
    }
    navigate('AddTransaction', { type });
  };

  const handleViewAllTransactions = () => {
    navigate('Transactions');
  };

  const handleViewReports = () => {
    navigate('Reports');
  };

  const renderHeader = () => (
    <View style={homeStyles.header}>
      <View style={homeStyles.headerTop}>
        <View style={homeStyles.headerLeft}>
          <Logo size={40} variant="rounded" style={homeStyles.headerLogo} />
          <View>
            <Text style={homeStyles.greeting}>
              {authState.isGuest ? 'Merhaba!' : `Merhaba, ${authState.user?.name || 'Kullanıcı'}!`}
            </Text>
            <Text style={homeStyles.date}>
              {DateFormatter.formatDate(new Date(), 'long')}
            </Text>
          </View>
        </View>
        {authState.isGuest && (
          <TouchableOpacity
            style={homeStyles.loginPrompt}
            onPress={() => navigate('Login')}
          >
            <Text style={homeStyles.loginPromptText}>Giriş Yap</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderSummaryCard = () => (
    <View style={homeStyles.summaryCard}>
      <View style={homeStyles.summaryHeader}>
        <Text style={homeStyles.summaryTitle}>Bu Ay</Text>
        <View style={homeStyles.periodSelector}>
          {(['week', 'month', 'year'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                homeStyles.periodButton,
                selectedPeriod === period && homeStyles.periodButtonActive
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text style={[
                homeStyles.periodButtonText,
                selectedPeriod === period && homeStyles.periodButtonTextActive
              ]}>
                {period === 'week' ? 'Hafta' : period === 'month' ? 'Ay' : 'Yıl'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={homeStyles.summaryContent}>
        {authState.isGuest || !summary ? (
          <View style={homeStyles.emptyState}>
            <Text style={homeStyles.emptyStateIcon}>📊</Text>
            <Text style={homeStyles.emptyStateTitle}>
              {authState.isGuest ? 'Giriş Yapın' : 'Veri Yükleniyor'}
            </Text>
            <Text style={homeStyles.emptyStateText}>
              {authState.isGuest
                ? 'Finansal özetinizi görmek için giriş yapın'
                : 'Finansal özet yükleniyor...'
              }
            </Text>
          </View>
        ) : (
          <>
            <View style={homeStyles.summaryItem}>
              <Text style={homeStyles.summaryLabel}>Gelir</Text>
              <Text style={[homeStyles.summaryAmount, homeStyles.incomeAmount]}>
                {NumberFormatter.formatCurrency(summary.totalIncome, summary.currency)}
              </Text>
            </View>

            <View style={homeStyles.summaryItem}>
              <Text style={homeStyles.summaryLabel}>Gider</Text>
              <Text style={[homeStyles.summaryAmount, homeStyles.expenseAmount]}>
                {NumberFormatter.formatCurrency(summary.totalExpense, summary.currency)}
              </Text>
            </View>

            <View style={[homeStyles.summaryItem, homeStyles.netIncomeItem]}>
              <Text style={homeStyles.summaryLabel}>Net</Text>
              <Text style={[
                homeStyles.summaryAmount,
                homeStyles.netAmount,
                summary.netIncome >= 0 ? homeStyles.incomeAmount : homeStyles.expenseAmount
              ]}>
                {NumberFormatter.formatCurrency(summary.netIncome, summary.currency)}
              </Text>
            </View>
          </>
        )}
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <View style={homeStyles.quickActions}>
      <Text style={homeStyles.sectionTitle}>Hızlı İşlemler</Text>
      <View style={homeStyles.actionButtons}>
        <TouchableOpacity
          style={[homeStyles.actionButton, homeStyles.incomeButton]}
          onPress={() => handleAddTransaction('income')}
        >
          <Text style={homeStyles.actionButtonIcon}>💰</Text>
          <Text style={homeStyles.actionButtonText}>Gelir Ekle</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[homeStyles.actionButton, homeStyles.expenseButton]}
          onPress={() => handleAddTransaction('expense')}
        >
          <Text style={homeStyles.actionButtonIcon}>💸</Text>
          <Text style={homeStyles.actionButtonText}>Gider Ekle</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[homeStyles.actionButton, homeStyles.reportButton]}
          onPress={handleViewReports}
        >
          <Text style={homeStyles.actionButtonIcon}>📊</Text>
          <Text style={homeStyles.actionButtonText}>Raporlar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderRecentTransactions = () => (
    <View style={homeStyles.recentTransactions}>
      <View style={homeStyles.sectionHeader}>
        <Text style={homeStyles.sectionTitle}>Son İşlemler</Text>
        <TouchableOpacity onPress={handleViewAllTransactions}>
          <Text style={homeStyles.viewAllText}>Tümünü Gör</Text>
        </TouchableOpacity>
      </View>

      {authState.isGuest ? (
        <View style={homeStyles.emptyState}>
          <Text style={homeStyles.emptyStateIcon}>🔐</Text>
          <Text style={homeStyles.emptyStateTitle}>Giriş Yapın</Text>
          <Text style={homeStyles.emptyStateText}>
            Son işlemlerinizi görmek için giriş yapın
          </Text>
        </View>
      ) : transactions && transactions.length > 0 ? (
        transactions.map((transaction) => (
          <TouchableOpacity
            key={transaction.id}
            style={homeStyles.transactionItem}
            onPress={() => navigate('TransactionDetails', { transactionId: transaction.id })}
          >
            <View style={homeStyles.transactionLeft}>
              <View style={[
                homeStyles.categoryIcon,
                { backgroundColor: transaction.categoryColor || colors.primary[100] }
              ]}>
                <Text style={homeStyles.categoryIconText}>
                  {transaction.type === 'income' ? '💰' : '💸'}
                </Text>
              </View>
              <View style={homeStyles.transactionInfo}>
                <Text style={homeStyles.transactionTitle}>{transaction.title}</Text>
                <Text style={homeStyles.transactionCategory}>
                  {transaction.categoryName} • {transaction.accountName}
                </Text>
                <Text style={homeStyles.transactionDate}>
                  {DateFormatter.getRelativeDateString(new Date(transaction.transactionDate))}
                </Text>
              </View>
            </View>
            <View style={homeStyles.transactionRight}>
              <Text style={[
                homeStyles.transactionAmount,
                transaction.type === 'income' ? homeStyles.incomeAmount : homeStyles.expenseAmount
              ]}>
                {transaction.type === 'expense' ? '-' : '+'}
                {NumberFormatter.formatCurrency(transaction.amount, transaction.currency)}
              </Text>
            </View>
          </TouchableOpacity>
        ))
      ) : (
        <View style={homeStyles.emptyState}>
          <Text style={homeStyles.emptyStateIcon}>📝</Text>
          <Text style={homeStyles.emptyStateTitle}>Henüz işlem yok</Text>
          <Text style={homeStyles.emptyStateText}>
            İlk gelir veya giderinizi ekleyerek başlayın
          </Text>
        </View>
      )}
    </View>
  );

  // Show loading spinner on initial load
  if (isLoading) {
    return (
      <SafeAreaView style={homeStyles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
        <LoadingSpinner
          size="large"
          text="Yükleniyor..."
          overlay={true}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={homeStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />

      <ScrollView
        style={homeStyles.scrollView}
        contentContainerStyle={homeStyles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}
        {renderSummaryCard()}
        {renderQuickActions()}
        {renderRecentTransactions()}
      </ScrollView>
    </SafeAreaView>
  );
};

// Use homeStyles from external file

export default HomeScreen;