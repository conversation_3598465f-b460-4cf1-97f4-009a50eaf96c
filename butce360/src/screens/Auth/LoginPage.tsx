import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { nativeAppleSignInService } from '../../services/nativeAppleSignInService';
import { nativeGoogleSignInService } from '../../services/nativeGoogleSignInService';

const LoginPage: React.FC = () => {
  const { navigate, goBack } = useNavigation();
  const { login, state } = useAuth();

  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isAppleLoading, setIsAppleLoading] = useState(false);
  const [appleSignInAvailable, setAppleSignInAvailable] = useState(false);

  // Check Apple Sign-In availability on component mount
  useEffect(() => {
    const checkAppleSignInAvailability = async () => {
      const available = await nativeAppleSignInService.isAvailable();
      console.log('[LoginPage] Apple Sign-In available:', available);
      setAppleSignInAvailable(available);
    };

    checkAppleSignInAvailability();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Kullanıcı adı gerekli';
    }

    if (!formData.password) {
      newErrors.password = 'Şifre gerekli';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Şifre en az 6 karakter olmalı';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      await login(formData);
      navigate('MainTabs');
    } catch (error) {
      Alert.alert(
        'Giriş Hatası',
        'Kullanıcı adı veya şifre hatalı. Lütfen tekrar deneyin.',
        [{ text: 'Tamam' }]
      );
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);

    try {
      const result = await nativeGoogleSignInService.signIn();

      if (result.success) {
        navigate('MainTabs');
      } else {
        Alert.alert(
          'Google Giriş Hatası',
          result.error || 'Google ile giriş yapılamadı',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Hata',
        'Google ile giriş yapılırken bir hata oluştu',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsGoogleLoading(false);
    }
  };

  const handleAppleSignIn = async () => {
    setIsAppleLoading(true);

    try {
      const result = await nativeAppleSignInService.signIn();

      if (result.success) {
        const userData = nativeAppleSignInService.formatUserData(result);
        console.log('[LoginPage] Apple Sign-In successful:', userData);

        // TODO: Send userData to backend for authentication
        navigate('MainTabs');
      } else {
        Alert.alert(
          'Apple Giriş Hatası',
          result.error || 'Apple ile giriş yapılamadı',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Hata',
        'Apple ile giriş yapılırken bir hata oluştu',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsAppleLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleRegister = () => {
    navigate('Register');
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Şifremi Unuttum',
      'Şifre sıfırlama özelliği yakında eklenecek.',
      [{ text: 'Tamam' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />

      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={goBack}>
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>
            <Text style={styles.title}>Giriş Yap</Text>
            <Text style={styles.subtitle}>
              Hesabınıza giriş yaparak tüm özelliklerden yararlanın
            </Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Kullanıcı Adı</Text>
              <TextInput
                style={[styles.input, errors.username && styles.inputError]}
                value={formData.username}
                onChangeText={(value) => handleInputChange('username', value)}
                placeholder="Kullanıcı adınızı girin"
                placeholderTextColor={colors.text.tertiary}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {errors.username && (
                <Text style={styles.errorText}>{errors.username}</Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Şifre</Text>
              <TextInput
                style={[styles.input, errors.password && styles.inputError]}
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                placeholder="Şifrenizi girin"
                placeholderTextColor={colors.text.tertiary}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
              {errors.password && (
                <Text style={styles.errorText}>{errors.password}</Text>
              )}
            </View>

            <TouchableOpacity style={styles.forgotPassword} onPress={handleForgotPassword}>
              <Text style={styles.forgotPasswordText}>Şifremi Unuttum</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, state.isLoading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={state.isLoading}
            >
              <Text style={styles.loginButtonText}>
                {state.isLoading ? 'Giriş Yapılıyor...' : 'Giriş Yap'}
              </Text>
            </TouchableOpacity>

            {/* Divider */}
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>veya</Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Google Sign-In Button */}
            <TouchableOpacity
              style={[styles.googleButton, isGoogleLoading && styles.googleButtonDisabled]}
              onPress={handleGoogleSignIn}
              disabled={isGoogleLoading}
            >
              <Text style={styles.googleButtonIcon}>G</Text>
              <Text style={styles.googleButtonText}>
                {isGoogleLoading ? 'Google ile giriş yapılıyor...' : 'Google ile Giriş Yap'}
              </Text>
            </TouchableOpacity>



            {/* Apple Sign-In Button */}
            {appleSignInAvailable && (
              <TouchableOpacity
                style={[styles.appleButton, isAppleLoading && styles.appleButtonDisabled]}
                onPress={handleAppleSignIn}
                disabled={isAppleLoading}
              >
                <Text style={styles.appleButtonIcon}>🍎</Text>
                <Text style={styles.appleButtonText}>
                  {isAppleLoading ? 'Apple ile giriş yapılıyor...' : 'Apple ile Giriş Yap'}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>Hesabınız yok mu?</Text>
            <TouchableOpacity onPress={handleRegister}>
              <Text style={styles.registerLink}>Hesap Oluştur</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing.screenPadding,
    paddingTop: spacing['4xl'],
    paddingBottom: spacing['2xl'],
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing['4xl'],
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text.primary,
  },
  title: {
    ...typography.styles.h2,
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 280,
  },
  form: {
    flex: 1,
    gap: spacing.lg,
  },
  inputGroup: {
    gap: spacing.sm,
  },
  label: {
    ...typography.styles.inputLabel,
    color: colors.text.primary,
  },
  input: {
    ...typography.styles.input,
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: spacing.cardRadius,
    paddingVertical: spacing.inputPaddingVertical,
    paddingHorizontal: spacing.inputPaddingHorizontal,
    backgroundColor: colors.surface.primary,
    color: colors.text.primary,
  },
  inputError: {
    borderColor: colors.error[500],
  },
  errorText: {
    ...typography.styles.inputError,
    color: colors.error[500],
  },
  forgotPassword: {
    alignSelf: 'flex-end',
  },
  forgotPasswordText: {
    ...typography.styles.body2,
    color: colors.primary[500],
  },
  loginButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },

  // Divider
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.border.primary,
  },
  dividerText: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    paddingHorizontal: spacing.md,
  },

  // Google Button
  googleButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#4285f4',
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.cardRadius,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginTop: spacing.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  googleButtonDisabled: {
    opacity: 0.6,
  },
  googleButtonIcon: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4285f4',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    width: 24,
    height: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  googleButtonText: {
    ...typography.styles.button,
    color: colors.text.primary,
    textTransform: 'none',
  },

  // Apple Button
  appleButton: {
    backgroundColor: '#000000',
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginTop: spacing.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  appleButtonDisabled: {
    opacity: 0.6,
  },
  appleButtonIcon: {
    fontSize: 20,
  },
  appleButtonText: {
    ...typography.styles.button,
    color: '#ffffff',
    textTransform: 'none',
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
    marginTop: spacing['2xl'],
  },
  footerText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
  },
  registerLink: {
    ...typography.styles.body2,
    color: colors.primary[500],
    fontWeight: '600',
  },
});

export default LoginPage;
