import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { NumberFormatter } from '../../utils/number';

const { width } = Dimensions.get('window');

// Mock data for reports
const mockReportData = {
  currentMonth: {
    totalIncome: 11000.00,
    totalExpense: 6750.25,
    netIncome: 4249.75,
    transactionCount: 67,
  },
  previousMonth: {
    totalIncome: 8500.00,
    totalExpense: 5200.50,
    netIncome: 3299.50,
    transactionCount: 52,
  },
  currentYear: {
    totalIncome: 95000.00,
    totalExpense: 62000.00,
    netIncome: 33000.00,
    transactionCount: 450,
  },
  previousYear: {
    totalIncome: 78000.00,
    totalExpense: 54000.00,
    netIncome: 24000.00,
    transactionCount: 380,
  },
  categoryBreakdown: [
    { name: 'Yiyecek & İçecek', amount: 2150.50, percentage: 31.8, color: '#ff6b6b', icon: '🍕' },
    { name: 'Ulaşım', amount: 1200.00, percentage: 17.8, color: '#4ecdc4', icon: '🚗' },
    { name: 'Alışveriş', amount: 950.75, percentage: 14.1, color: '#45b7d1', icon: '🛍️' },
    { name: 'Eğlence', amount: 800.00, percentage: 11.8, color: '#96ceb4', icon: '🎬' },
    { name: 'Faturalar', amount: 650.00, percentage: 9.6, color: '#ffeaa7', icon: '📄' },
    { name: 'Diğer', amount: 999.00, percentage: 14.9, color: '#95a5a6', icon: '📦' },
  ],
  monthlyTrend: [
    { month: 'Oca', income: 7500, expense: 4200 },
    { month: 'Şub', income: 8200, expense: 4800 },
    { month: 'Mar', income: 8500, expense: 5200 },
    { month: 'Nis', income: 9200, expense: 5800 },
    { month: 'May', income: 11000, expense: 6750 },
  ],
};

const ReportsScreen: React.FC = () => {
  const { navigate, goBack } = useNavigation();
  const { state: authState } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'year'>('month');
  const [currentData, setCurrentData] = useState(mockReportData.currentMonth);
  const [previousData, setPreviousData] = useState(mockReportData.previousMonth);

  // Update data when period changes
  useEffect(() => {
    if (selectedPeriod === 'month') {
      setCurrentData(mockReportData.currentMonth);
      setPreviousData(mockReportData.previousMonth);
    } else {
      setCurrentData(mockReportData.currentYear);
      setPreviousData(mockReportData.previousYear);
    }
  }, [selectedPeriod]);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };



  const incomeChange = ((currentData.totalIncome - previousData.totalIncome) / previousData.totalIncome) * 100;
  const expenseChange = ((currentData.totalExpense - previousData.totalExpense) / previousData.totalExpense) * 100;
  const netChange = ((currentData.netIncome - previousData.netIncome) / previousData.netIncome) * 100;

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <TouchableOpacity
        style={[
          styles.periodButton,
          selectedPeriod === 'month' && styles.periodButtonActive
        ]}
        onPress={() => setSelectedPeriod('month')}
      >
        <Text style={[
          styles.periodButtonText,
          selectedPeriod === 'month' && styles.periodButtonTextActive
        ]}>
          Bu Ay
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.periodButton,
          selectedPeriod === 'year' && styles.periodButtonActive
        ]}
        onPress={() => setSelectedPeriod('year')}
      >
        <Text style={[
          styles.periodButtonText,
          selectedPeriod === 'year' && styles.periodButtonTextActive
        ]}>
          Bu Yıl
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderSummaryCards = () => (
    <View style={styles.summaryContainer}>
      <View style={styles.summaryCard}>
        <Text style={styles.summaryLabel}>Toplam Gelir</Text>
        <Text style={[styles.summaryAmount, styles.incomeAmount]}>
          {NumberFormatter.formatCurrency(currentData.totalIncome, 'TRY')}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[
            styles.changeText,
            incomeChange >= 0 ? styles.positiveChange : styles.negativeChange
          ]}>
            {incomeChange >= 0 ? '↗' : '↘'} {Math.abs(incomeChange).toFixed(1)}%
          </Text>
          <Text style={styles.changeLabel}>önceki aya göre</Text>
        </View>
      </View>

      <View style={styles.summaryCard}>
        <Text style={styles.summaryLabel}>Toplam Gider</Text>
        <Text style={[styles.summaryAmount, styles.expenseAmount]}>
          {NumberFormatter.formatCurrency(currentData.totalExpense, 'TRY')}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[
            styles.changeText,
            expenseChange <= 0 ? styles.positiveChange : styles.negativeChange
          ]}>
            {expenseChange >= 0 ? '↗' : '↘'} {Math.abs(expenseChange).toFixed(1)}%
          </Text>
          <Text style={styles.changeLabel}>önceki aya göre</Text>
        </View>
      </View>

      <View style={[styles.summaryCard, styles.netCard]}>
        <Text style={styles.summaryLabel}>Net Gelir</Text>
        <Text style={[
          styles.summaryAmount, 
          styles.netAmount,
          currentData.netIncome >= 0 ? styles.incomeAmount : styles.expenseAmount
        ]}>
          {NumberFormatter.formatCurrency(currentData.netIncome, 'TRY')}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[
            styles.changeText,
            netChange >= 0 ? styles.positiveChange : styles.negativeChange
          ]}>
            {netChange >= 0 ? '↗' : '↘'} {Math.abs(netChange).toFixed(1)}%
          </Text>
          <Text style={styles.changeLabel}>önceki aya göre</Text>
        </View>
      </View>
    </View>
  );

  const renderCategoryBreakdown = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Kategori Dağılımı</Text>
        <TouchableOpacity onPress={() => navigate('CategoryReport')}>
          <Text style={styles.viewAllText}>Detaylar</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.categoryList}>
        {mockReportData.categoryBreakdown.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <View style={styles.categoryLeft}>
              <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                <Text style={styles.categoryIconText}>{category.icon}</Text>
              </View>
              <View style={styles.categoryInfo}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryAmount}>
                  {NumberFormatter.formatCurrency(category.amount, 'TRY')}
                </Text>
              </View>
            </View>
            <View style={styles.categoryRight}>
              <Text style={styles.categoryPercentage}>{category.percentage}%</Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { 
                      width: `${category.percentage}%`,
                      backgroundColor: category.color 
                    }
                  ]} 
                />
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const renderMonthlyTrend = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Aylık Trend</Text>
        <TouchableOpacity onPress={() => navigate('MonthlyReport')}>
          <Text style={styles.viewAllText}>Detaylar</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.chartContainer}>
        <View style={styles.chartPlaceholder}>
          <Text style={styles.chartPlaceholderText}>📊</Text>
          <Text style={styles.chartPlaceholderTitle}>Grafik Görünümü</Text>
          <Text style={styles.chartPlaceholderSubtitle}>
            Gelir ve gider trendlerinizi görselleştirin
          </Text>
        </View>
      </View>

      <View style={styles.trendList}>
        {mockReportData.monthlyTrend.map((item, index) => (
          <View key={index} style={styles.trendItem}>
            <Text style={styles.trendMonth}>{item.month}</Text>
            <View style={styles.trendAmounts}>
              <Text style={[styles.trendAmount, styles.incomeAmount]}>
                {NumberFormatter.formatLargeNumber(item.income)}
              </Text>
              <Text style={[styles.trendAmount, styles.expenseAmount]}>
                -{NumberFormatter.formatLargeNumber(item.expense)}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const renderQuickStats = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Hızlı İstatistikler</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{currentData.transactionCount}</Text>
          <Text style={styles.statLabel}>Bu Ay İşlem</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {NumberFormatter.formatCurrency(currentData.totalExpense / currentData.transactionCount, 'TRY', false)}
          </Text>
          <Text style={styles.statLabel}>Ortalama Gider</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {mockReportData.categoryBreakdown[0].name}
          </Text>
          <Text style={styles.statLabel}>En Çok Harcanan</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {((currentData.netIncome / currentData.totalIncome) * 100).toFixed(0)}%
          </Text>
          <Text style={styles.statLabel}>Tasarruf Oranı</Text>
        </View>
      </View>
    </View>
  );

  if (authState.isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={goBack}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Raporlar</Text>
          <View style={styles.headerRight} />
        </View>

        <View style={styles.guestContainer}>
          <Text style={styles.guestIcon}>📊</Text>
          <Text style={styles.guestTitle}>Raporları Görüntüle</Text>
          <Text style={styles.guestText}>
            Detaylı raporlar ve analizler için giriş yapmanız gerekiyor.
          </Text>
          <TouchableOpacity 
            style={styles.loginButton} 
            onPress={() => navigate('Login')}
          >
            <Text style={styles.loginButtonText}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Raporlar</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Period Selector */}
      {renderPeriodSelector()}

      {/* Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderSummaryCards()}
        {renderCategoryBreakdown()}
        {renderMonthlyTrend()}
        {renderQuickStats()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text.primary,
  },
  title: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  headerRight: {
    width: 40,
  },

  // Period Selector
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  periodButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: spacing.cardRadius,
    backgroundColor: colors.background.secondary,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: colors.primary[500],
  },
  periodButtonText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    fontWeight: '600',
  },
  periodButtonTextActive: {
    color: colors.text.inverse,
  },

  // Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing['4xl'],
  },

  // Summary Cards
  summaryContainer: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
    gap: spacing.md,
  },
  summaryCard: {
    backgroundColor: colors.surface.primary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  netCard: {
    backgroundColor: colors.primary[50],
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  summaryLabel: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
  },
  summaryAmount: {
    ...typography.styles.currency,
    fontWeight: '700',
    marginBottom: spacing.sm,
  },
  netAmount: {
    fontSize: 28,
  },
  incomeAmount: {
    color: colors.secondary[600],
  },
  expenseAmount: {
    color: colors.accent[600],
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  changeText: {
    ...typography.styles.caption,
    fontWeight: '600',
  },
  changeLabel: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
  },
  positiveChange: {
    color: colors.secondary[600],
  },
  negativeChange: {
    color: colors.accent[600],
  },

  // Sections
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
  },
  viewAllText: {
    ...typography.styles.body2,
    color: colors.primary[500],
    fontWeight: '600',
  },

  // Category Breakdown
  categoryList: {
    gap: spacing.md,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.surface.primary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  categoryIconText: {
    fontSize: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    ...typography.styles.body2,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  categoryAmount: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  categoryRight: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  categoryPercentage: {
    ...typography.styles.body2,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  progressBar: {
    width: 60,
    height: 4,
    backgroundColor: colors.background.secondary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },

  // Chart
  chartContainer: {
    marginBottom: spacing.lg,
  },
  chartPlaceholder: {
    backgroundColor: colors.surface.primary,
    padding: spacing['4xl'],
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  chartPlaceholderText: {
    fontSize: 48,
    marginBottom: spacing.lg,
  },
  chartPlaceholderTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  chartPlaceholderSubtitle: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  // Trend
  trendList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.surface.primary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  trendItem: {
    alignItems: 'center',
    flex: 1,
  },
  trendMonth: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
  },
  trendAmounts: {
    alignItems: 'center',
    gap: spacing.xs,
  },
  trendAmount: {
    ...typography.styles.caption,
    fontWeight: '600',
  },

  // Stats Grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  statItem: {
    backgroundColor: colors.surface.primary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    width: (width - spacing.screenPadding * 2 - spacing.md) / 2,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statValue: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  statLabel: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.screenPadding,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: spacing.xl,
  },
  guestTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  guestText: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: spacing['2xl'],
  },
  loginButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.buttonPaddingVertical,
    paddingHorizontal: spacing.buttonPaddingHorizontal,
    borderRadius: spacing.cardRadius,
  },
  loginButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },
});

export default ReportsScreen;
