import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '../../navigation/AppNavigator';
import { transactionService } from '../../services/transactionService';
import { Transaction } from '../../types/models';
import { NumberFormatter } from '../../utils/number';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const TransactionsScreen: React.FC = () => {
  const { state: authState } = useAuth();
  const { navigate } = useNavigation();

  // State
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);

  // Fetch transactions from API
  const fetchTransactions = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      setError(null);
      if (append) {
        setIsLoadingMore(true);
      }

      // Only fetch data if user is authenticated (not guest)
      if (authState.isAuthenticated && !authState.isGuest) {
        const response = await transactionService.getTransactions(page, 20);
        const newTransactions = response.transactions || [];

        if (append) {
          setTransactions(prev => [...prev, ...newTransactions]);
        } else {
          setTransactions(newTransactions);
          setCurrentPage(1);
        }

        // Check if there's more data
        setHasMoreData(newTransactions.length === 20);
      } else {
        // For guest users, show empty state
        setTransactions([]);
        setHasMoreData(false);
      }
    } catch (fetchError) {
      console.error('[TransactionsScreen] Error fetching transactions:', fetchError);
      setError(fetchError instanceof Error ? fetchError.message : 'İşlemler yüklenirken hata oluştu');
      if (!append) {
        setTransactions([]);
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setIsLoadingMore(false);
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  // Initial load
  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions, authState.isAuthenticated, authState.isGuest]);

  // Pull to refresh
  const onRefresh = () => {
    setIsRefreshing(true);
    fetchTransactions();
  };

  // Load more data
  const loadMore = () => {
    if (!isLoadingMore && hasMoreData && authState.isAuthenticated && !authState.isGuest) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchTransactions(nextPage, true);
    }
  };

  // Guest state
  if (authState.isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        {/* Header like ReportsScreen */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigate('Home')}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.title}>İşlemlerim</Text>
          <View style={styles.headerRight} />
        </View>

        <View style={styles.guestContainer}>
          <Text style={styles.guestIcon}>📝</Text>
          <Text style={styles.guestTitle}>İşlemlerinizi Görüntüle</Text>
          <Text style={styles.guestText}>
            Gelir ve gider takibi için giriş yapmanız gerekiyor.
          </Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigate('Login')}
          >
            <Text style={styles.loginButtonText}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigate('Home')}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.title}>İşlemlerim</Text>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.loadingContainer}>
          <LoadingSpinner />
        </View>
      </SafeAreaView>
    );
  }

  // Authenticated user with data
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigate('Home')}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>İşlemlerim</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigate('AddTransaction')}
        >
          <Text style={styles.addButtonText}>+ Ekle</Text>
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => fetchTransactions()}>
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={['#0ea5e9']}
            tintColor="#0ea5e9"
          />
        }
      >
        {transactions.length > 0 ? (
          <View style={styles.transactionList}>
            {transactions
              .sort((a, b) => new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime())
              .map((transaction) => (
              <View key={transaction.id} style={styles.transactionItem}>
                <View style={styles.transactionLeft}>
                  <Text style={styles.transactionDescription}>
                    {transaction.title || 'İsimsiz İşlem'}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {transaction.transactionDate
                      ? new Date(transaction.transactionDate).toLocaleDateString('tr-TR')
                      : 'Tarih belirtilmemiş'
                    }
                  </Text>
                  {transaction.note && (
                    <Text style={styles.transactionCategory}>
                      {transaction.note}
                    </Text>
                  )}
                </View>
                <View style={styles.transactionRight}>
                  <Text style={[
                    styles.transactionAmount,
                    transaction.type === 'income' ? styles.incomeAmount : styles.expenseAmount
                  ]}>
                    {transaction.type === 'income' ? '+' : '-'}
                    {NumberFormatter.formatCurrency(transaction.amount, transaction.currency)}
                  </Text>
                </View>
              </View>
            ))}

            {/* Load More Button */}
            {hasMoreData && !isLoading && (
              <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={loadMore}
                disabled={isLoadingMore}
              >
                <Text style={styles.loadMoreText}>
                  {isLoadingMore ? 'Yükleniyor...' : 'Daha Fazla Yükle'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>📝</Text>
            <Text style={styles.emptyTitle}>Henüz işlem yok</Text>
            <Text style={styles.emptyText}>
              İlk gelir veya giderinizi ekleyerek başlayın
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={() => navigate('AddTransaction')}
            >
              <Text style={styles.emptyStateButtonText}>İşlem Ekle</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  // Header like ReportsScreen
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: '#1e293b',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  headerRight: {
    width: 40,
  },
  addButton: {
    backgroundColor: '#0ea5e9',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  guestTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  guestText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },
  loginButton: {
    backgroundColor: '#0ea5e9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  
  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },

  // Empty State Button
  emptyStateButton: {
    backgroundColor: '#0ea5e9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Error
  errorContainer: {
    backgroundColor: '#fef2f2',
    margin: 20,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginBottom: 8,
  },
  retryButton: {
    alignSelf: 'flex-start',
  },
  retryButtonText: {
    color: '#ef4444',
    fontSize: 14,
    fontWeight: '600',
  },

  // ScrollView
  scrollView: {
    flex: 1,
  },

  // Transaction List
  transactionList: {
    padding: 20,
  },
  transactionItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  transactionLeft: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 12,
    color: '#64748b',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  incomeAmount: {
    color: '#22c55e',
  },
  expenseAmount: {
    color: '#ef4444',
  },
  loadMoreButton: {
    backgroundColor: '#f8fafc',
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    alignItems: 'center',
  },
  loadMoreText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0ea5e9',
  },
});

export default TransactionsScreen;
