import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Modal,
  FlatList,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { NumberFormatter } from '../../utils/number';
import { DateFormatter } from '../../utils/date';
import { categoryService } from '../../services/categoryService';
import { accountService } from '../../services/accountService';
import { transactionService } from '../../services/transactionService';
import { Category, Account } from '../../types/models';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { styles } from './styles';

// ModalSeparator will be defined after modalStyles

// Amount input style
const amountInputStyle = {
  textAlign: 'right' as const,
  fontSize: 18,
  fontWeight: '600' as const,
};

interface AddTransactionScreenProps {
  route?: {
    params?: {
      type?: 'income' | 'expense';
      transactionId?: string;
    };
  };
}

// Modal types
type ModalType = 'category' | 'account' | 'date' | null;

const AddTransactionScreen: React.FC<AddTransactionScreenProps> = ({ route }) => {
  const { navigate, goBack } = useNavigation();
  const { state: authState } = useAuth();

  const transactionType = route?.params?.type || 'expense';
  const isEditing = !!route?.params?.transactionId;

  // Form data
  const [formData, setFormData] = useState({
    title: '',
    type: transactionType,
    amount: '',
    categoryId: '',
    accountId: '',
    note: '',
    transactionDate: new Date(),
  });

  // UI states
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [activeModal, setActiveModal] = useState<ModalType>(null);

  // Data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Fetch categories and accounts
  const fetchData = useCallback(async () => {
    try {
      setIsLoadingData(true);

      if (authState.isAuthenticated && !authState.isGuest) {
        const [categoriesData, accountsData] = await Promise.all([
          categoryService.getCategories(),
          accountService.getAccounts()
        ]);

        setCategories(categoriesData || []);
        setAccounts(accountsData || []);

        // Set default account if available
        if (accountsData && accountsData.length > 0 && !formData.accountId) {
          setFormData(prev => ({ ...prev, accountId: accountsData[0].id || '' }));
        }
      }
    } catch (error) {
      console.error('[AddTransactionScreen] Error fetching data:', error);
      Alert.alert('Hata', 'Veriler yüklenirken hata oluştu');
    } finally {
      setIsLoadingData(false);
    }
  }, [authState.isAuthenticated, authState.isGuest, formData.accountId]);

  // Load data on mount
  useEffect(() => {
    fetchData();
  }, [fetchData, authState.isAuthenticated, authState.isGuest]);

  // Filter categories by type
  const filteredCategories = categories.filter(cat => cat.type === formData.type);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'İşlem başlığı gerekli';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Tutar gerekli';
    } else if (!NumberFormatter.isValidNumber(formData.amount)) {
      newErrors.amount = 'Geçerli bir tutar girin';
    } else if (NumberFormatter.parseCurrency(formData.amount) <= 0) {
      newErrors.amount = 'Tutar sıfırdan büyük olmalı';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Kategori seçimi gerekli';
    }

    if (!formData.accountId) {
      newErrors.accountId = 'Hesap seçimi gerekli';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    // Check if user is in guest mode
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'İşlem eklemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => navigate('Login') },
        ]
      );
      return;
    }

    setIsLoading(true);

    try {
      // Prepare transaction data (matching web payload format)
      const transactionData = {
        title: formData.title,
        amount: NumberFormatter.parseCurrency(formData.amount),
        type: formData.type,
        currency: 'TL',
        category_id: formData.categoryId,
        account_id: formData.accountId,
        transaction_date: DateFormatter.formatForApi(formData.transactionDate),
        payment_method: 'card', // Default payment method
        note: formData.note,
      };

      console.log('[AddTransactionScreen] Form data:', formData);
      console.log('[AddTransactionScreen] Saving transaction:', transactionData);

      // Call API
      if (isEditing) {
        // TODO: Implement update transaction
        await transactionService.updateTransaction(route?.params?.transactionId || '', transactionData);
      } else {
        await transactionService.createTransaction(transactionData);
      }

      Alert.alert(
        'Başarılı',
        `${formData.type === 'income' ? 'Gelir' : 'Gider'} ${isEditing ? 'güncellendi' : 'eklendi'}.`,
        [{ text: 'Tamam', onPress: () => goBack() }]
      );
    } catch (error) {
      console.error('[AddTransactionScreen] Error saving transaction:', error);
      Alert.alert(
        'Hata',
        error instanceof Error ? error.message : 'İşlem kaydedilirken bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleTypeChange = (type: 'income' | 'expense') => {
    setFormData(prev => ({ 
      ...prev, 
      type, 
      categoryId: '' // Reset category when type changes
    }));
    setErrors(prev => ({ ...prev, categoryId: '' }));
  };

  // Selection handlers
  const handleCategorySelect = (category: Category) => {
    setFormData(prev => ({ ...prev, categoryId: category.id || '' }));
    setErrors(prev => ({ ...prev, categoryId: '' }));
    setActiveModal(null);
  };

  const handleAccountSelect = (account: Account) => {
    setFormData(prev => ({ ...prev, accountId: account.id || '' }));
    setErrors(prev => ({ ...prev, accountId: '' }));
    setActiveModal(null);
  };

  // Get selected items
  const selectedCategory = filteredCategories.find(cat => cat.id === formData.categoryId);
  const selectedAccount = accounts.find(acc => acc.id === formData.accountId);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={goBack}>
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>
            <Text style={styles.title}>
              {isEditing ? 'İşlemi Düzenle' : 'Yeni İşlem'}
            </Text>
            <View style={styles.headerRight} />
          </View>

          {/* Type Selector */}
          <View style={styles.typeSelector}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                styles.incomeButton,
                formData.type === 'income' && styles.typeButtonActive
              ]}
              onPress={() => handleTypeChange('income')}
            >
              <Text style={[
                styles.typeButtonText,
                formData.type === 'income' && styles.typeButtonTextActive
              ]}>
                💰 Gelir
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                styles.expenseButton,
                formData.type === 'expense' && styles.typeButtonActive
              ]}
              onPress={() => handleTypeChange('expense')}
            >
              <Text style={[
                styles.typeButtonText,
                formData.type === 'expense' && styles.typeButtonTextActive
              ]}>
                💸 Gider
              </Text>
            </TouchableOpacity>
          </View>

          {/* Form */}
          <View style={styles.form}>
            {/* Title */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>İşlem Başlığı</Text>
              <TextInput
                style={[styles.input, errors.title && styles.inputError]}
                value={formData.title}
                onChangeText={(value) => handleInputChange('title', value)}
                placeholder="Örn: Market alışverişi"
                placeholderTextColor={colors.text.tertiary}
              />
              {errors.title && (
                <Text style={styles.errorText}>{errors.title}</Text>
              )}
            </View>

            {/* Amount */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Tutar</Text>
              <TextInput
                style={[styles.input, amountInputStyle, errors.amount && styles.inputError]}
                value={formData.amount}
                onChangeText={(value) => handleInputChange('amount', value)}
                placeholder="0,00"
                placeholderTextColor={colors.text.tertiary}
                keyboardType="numeric"
              />
              {errors.amount && (
                <Text style={styles.errorText}>{errors.amount}</Text>
              )}
            </View>

            {/* Category */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Kategori</Text>
              <TouchableOpacity
                style={[styles.selector, errors.categoryId && styles.inputError]}
                onPress={() => setActiveModal('category')}
                disabled={isLoadingData || filteredCategories.length === 0}
              >
                {selectedCategory ? (
                  <View style={styles.selectedItem}>
                    <View style={[
                      styles.selectedCategoryIcon,
                      { backgroundColor: selectedCategory.color || '#0ea5e9' }
                    ]}>
                      <Text style={styles.selectedCategoryIconText}>{selectedCategory.icon || '📁'}</Text>
                    </View>
                    <Text style={styles.selectedItemText}>{selectedCategory.name}</Text>
                  </View>
                ) : (
                  <Text style={styles.selectorPlaceholder}>
                    {isLoadingData ? 'Yükleniyor...' :
                     filteredCategories.length === 0 ? 'Kategori bulunamadı' :
                     'Kategori seçin'}
                  </Text>
                )}
                <Text style={styles.selectorArrow}>›</Text>
              </TouchableOpacity>
              {errors.categoryId && (
                <Text style={styles.errorText}>{errors.categoryId}</Text>
              )}
            </View>

            {/* Account */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Hesap</Text>
              <TouchableOpacity
                style={[styles.selector, errors.accountId && styles.inputError]}
                onPress={() => setActiveModal('account')}
                disabled={isLoadingData || accounts.length === 0}
              >
                {selectedAccount ? (
                  <View style={styles.selectedItem}>
                    <View style={[styles.accountColor, { backgroundColor: selectedAccount.color || '#0ea5e9' }]} />
                    <Text style={styles.selectedItemText}>{selectedAccount.name}</Text>
                  </View>
                ) : (
                  <Text style={styles.selectorPlaceholder}>
                    {isLoadingData ? 'Yükleniyor...' :
                     accounts.length === 0 ? 'Hesap bulunamadı' :
                     'Hesap seçin'}
                  </Text>
                )}
                <Text style={styles.selectorArrow}>›</Text>
              </TouchableOpacity>
              {errors.accountId && (
                <Text style={styles.errorText}>{errors.accountId}</Text>
              )}
            </View>

            {/* Note */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Not (İsteğe bağlı)</Text>
              <TextInput
                style={[styles.input, styles.noteInput]}
                value={formData.note}
                onChangeText={(value) => handleInputChange('note', value)}
                placeholder="İşlemle ilgili notlar..."
                placeholderTextColor={colors.text.tertiary}
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Date */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Tarih</Text>
              <TouchableOpacity 
                style={styles.selector}
                onPress={() => {
                  // TODO: Open date picker modal
                  Alert.alert('Tarih Seçimi', 'Tarih seçim modalı yakında eklenecek.');
                }}
              >
                <Text style={styles.selectedItemText}>
                  {DateFormatter.formatDate(formData.transactionDate)}
                </Text>
                <Text style={styles.selectorArrow}>›</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Save Button */}
          <TouchableOpacity 
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Kaydediliyor...' : isEditing ? 'Güncelle' : 'Kaydet'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Category Selection Modal */}
      <Modal
        visible={activeModal === 'category'}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setActiveModal(null)}
      >
        <SafeAreaView style={modalStyles.container}>
          <View style={modalStyles.header}>
            <TouchableOpacity onPress={() => setActiveModal(null)}>
              <Text style={modalStyles.cancelButton}>İptal</Text>
            </TouchableOpacity>
            <Text style={modalStyles.title}>Kategori Seçin</Text>
            <View style={modalStyles.headerRight} />
          </View>

          {isLoadingData ? (
            <View style={modalStyles.loadingContainer}>
              <LoadingSpinner />
            </View>
          ) : (
            <FlatList
              data={filteredCategories}
              keyExtractor={(item) => item.id || ''}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    modalStyles.listItem,
                    formData.categoryId === item.id && modalStyles.listItemSelected
                  ]}
                  onPress={() => handleCategorySelect(item)}
                >
                  <View style={modalStyles.itemLeft}>
                    <View style={[
                      modalStyles.categoryIconCircle,
                      { backgroundColor: item.color || '#0ea5e9' }
                    ]}>
                      <Text style={modalStyles.categoryIconText}>{item.icon || '📁'}</Text>
                    </View>
                    <View style={modalStyles.categoryInfo}>
                      <Text style={modalStyles.categoryTitle}>{item.name}</Text>
                      <Text style={modalStyles.categorySubtitle}>
                        {formData.type === 'income' ? 'Gelir' : 'Gider'} kategorisi
                      </Text>
                    </View>
                  </View>
                  {formData.categoryId === item.id && (
                    <View style={modalStyles.selectedIndicator}>
                      <Text style={modalStyles.checkmark}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              ItemSeparatorComponent={ModalSeparator}
            />
          )}
        </SafeAreaView>
      </Modal>

      {/* Account Selection Modal */}
      <Modal
        visible={activeModal === 'account'}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setActiveModal(null)}
      >
        <SafeAreaView style={modalStyles.container}>
          <View style={modalStyles.header}>
            <TouchableOpacity onPress={() => setActiveModal(null)}>
              <Text style={modalStyles.cancelButton}>İptal</Text>
            </TouchableOpacity>
            <Text style={modalStyles.title}>Hesap Seçin</Text>
            <View style={modalStyles.headerRight} />
          </View>

          {isLoadingData ? (
            <View style={modalStyles.loadingContainer}>
              <LoadingSpinner />
            </View>
          ) : (
            <FlatList
              data={accounts}
              keyExtractor={(item) => item.id || ''}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    modalStyles.listItem,
                    formData.accountId === item.id && modalStyles.listItemSelected
                  ]}
                  onPress={() => handleAccountSelect(item)}
                >
                  <View style={modalStyles.itemLeft}>
                    <View style={[
                      modalStyles.accountIconCircle,
                      { backgroundColor: item.color || '#0ea5e9' }
                    ]}>
                      <Text style={modalStyles.accountIconText}>
                        {item.type === 'credit' ? '💳' :
                         item.type === 'cash' ? '💵' : '🏦'}
                      </Text>
                    </View>
                    <View style={modalStyles.accountInfo}>
                      <Text style={modalStyles.accountTitle}>{item.name}</Text>
                      <Text style={modalStyles.accountSubtitle}>
                        {item.type === 'credit' ? 'Kredi Kartı' :
                         item.type === 'cash' ? 'Nakit' : 'Banka Hesabı'}
                      </Text>
                    </View>
                  </View>
                  {formData.accountId === item.id && (
                    <View style={modalStyles.selectedIndicator}>
                      <Text style={modalStyles.checkmark}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              ItemSeparatorComponent={ModalSeparator}
            />
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

// Modal styles
const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  cancelButton: {
    fontSize: 16,
    color: '#0ea5e9',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  headerRight: {
    width: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Category List Styles (like Account)
  categoryIconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  categoryIconText: {
    fontSize: 24,
    color: '#ffffff',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  categorySubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  selectedIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#0ea5e9',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Account List Styles
  accountIconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  accountIconText: {
    fontSize: 24,
    color: '#ffffff',
  },
  accountInfo: {
    flex: 1,
  },
  accountTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  accountSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },

  // List Layout for Categories and Accounts
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
  },
  listItemSelected: {
    backgroundColor: '#f0f9ff',
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  accountDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  itemText: {
    fontSize: 16,
    color: '#1e293b',
  },
  checkmark: {
    fontSize: 18,
    color: '#0ea5e9',
    fontWeight: 'bold',
  },
  separator: {
    height: 1,
    backgroundColor: '#f1f5f9',
    marginLeft: 20,
  },

});

// Separator component for modal lists
const ModalSeparator = () => <View style={modalStyles.separator} />;

export default AddTransactionScreen;
