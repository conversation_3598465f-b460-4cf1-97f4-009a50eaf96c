import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

// Mock categories data
const mockCategories = {
  income: [
    { id: '1', name: '<PERSON><PERSON><PERSON>', color: '#2ecc71', icon: '💰', isDefault: true, transactionCount: 12 },
    { id: '2', name: 'Freelance', color: '#3498db', icon: '💻', isDefault: false, transactionCount: 5 },
    { id: '3', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', color: '#9b59b6', icon: '📈', isDefault: false, transactionCount: 3 },
    { id: '4', name: 'Hediye', color: '#e74c3c', icon: '🎁', isDefault: false, transactionCount: 2 },
    { id: '5', name: 'Diğer Gelir', color: '#f39c12', icon: '💵', isDefault: true, transactionCount: 1 },
  ],
  expense: [
    { id: '6', name: 'Yiyecek & İçecek', color: '#ff6b6b', icon: '🍕', isDefault: true, transactionCount: 25 },
    { id: '7', name: 'Ulaşım', color: '#4ecdc4', icon: '🚗', isDefault: true, transactionCount: 18 },
    { id: '8', name: 'Alışveriş', color: '#45b7d1', icon: '🛍️', isDefault: true, transactionCount: 15 },
    { id: '9', name: 'Eğlence', color: '#96ceb4', icon: '🎬', isDefault: true, transactionCount: 12 },
    { id: '10', name: 'Faturalar', color: '#ffeaa7', icon: '📄', isDefault: true, transactionCount: 8 },
    { id: '11', name: 'Sağlık', color: '#dda0dd', icon: '🏥', isDefault: true, transactionCount: 4 },
    { id: '12', name: 'Eğitim', color: '#98d8c8', icon: '📚', isDefault: true, transactionCount: 3 },
    { id: '13', name: 'Seyahat', color: '#f7dc6f', icon: '✈️', isDefault: true, transactionCount: 2 },
  ],
};

const CategoriesScreen: React.FC = () => {
  const { navigate, goBack } = useNavigation();
  const { state: authState } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'income' | 'expense'>('expense');

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleAddCategory = () => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Kategori eklemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => navigate('Login') },
        ]
      );
      return;
    }
    navigate('AddCategory', { type: selectedTab });
  };

  const handleCategoryPress = (categoryId: string) => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Kategori düzenlemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => navigate('Login') },
        ]
      );
      return;
    }
    navigate('EditCategory', { categoryId });
  };

  const handleDeleteCategory = (categoryId: string, categoryName: string, isDefault: boolean) => {
    if (isDefault) {
      Alert.alert(
        'Varsayılan Kategori',
        'Varsayılan kategoriler silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    Alert.alert(
      'Kategoriyi Sil',
      `"${categoryName}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            // TODO: Implement delete category
            Alert.alert('Başarılı', 'Kategori silindi.');
          }
        },
      ]
    );
  };

  const currentCategories = mockCategories[selectedTab];

  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[
          styles.tabButton,
          selectedTab === 'income' && styles.tabButtonActive
        ]}
        onPress={() => setSelectedTab('income')}
      >
        <Text style={[
          styles.tabButtonText,
          selectedTab === 'income' && styles.tabButtonTextActive
        ]}>
          💰 Gelir ({mockCategories.income.length})
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tabButton,
          selectedTab === 'expense' && styles.tabButtonActive
        ]}
        onPress={() => setSelectedTab('expense')}
      >
        <Text style={[
          styles.tabButtonText,
          selectedTab === 'expense' && styles.tabButtonTextActive
        ]}>
          💸 Gider ({mockCategories.expense.length})
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderCategoryItem = (category: typeof currentCategories[0]) => (
    <TouchableOpacity
      key={category.id}
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(category.id)}
    >
      <View style={styles.categoryLeft}>
        <View style={[
          styles.categoryIcon,
          { backgroundColor: category.color }
        ]}>
          <Text style={styles.categoryIconText}>
            {category.icon}
          </Text>
        </View>
        <View style={styles.categoryInfo}>
          <View style={styles.categoryHeader}>
            <Text style={styles.categoryName}>{category.name}</Text>
            {category.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>Varsayılan</Text>
              </View>
            )}
          </View>
          <Text style={styles.categoryStats}>
            {category.transactionCount} işlem
          </Text>
        </View>
      </View>
      <View style={styles.categoryRight}>
        <TouchableOpacity
          style={styles.moreButton}
          onPress={() => {
            Alert.alert(
              category.name,
              'Ne yapmak istiyorsunuz?',
              [
                { text: 'İptal', style: 'cancel' },
                { text: 'Düzenle', onPress: () => handleCategoryPress(category.id) },
                ...(category.isDefault ? [] : [
                  { 
                    text: 'Sil', 
                    style: 'destructive' as const,
                    onPress: () => handleDeleteCategory(category.id, category.name, category.isDefault)
                  }
                ]),
              ]
            );
          }}
        >
          <Text style={styles.moreButtonText}>⋯</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>
        {selectedTab === 'income' ? '💰' : '💸'}
      </Text>
      <Text style={styles.emptyStateTitle}>
        {selectedTab === 'income' ? 'Henüz gelir kategorisi yok' : 'Henüz gider kategorisi yok'}
      </Text>
      <Text style={styles.emptyStateText}>
        {selectedTab === 'income' 
          ? 'İlk gelir kategorinizi ekleyerek başlayın' 
          : 'İlk gider kategorinizi ekleyerek başlayın'}
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddCategory}>
        <Text style={styles.addButtonText}>+ Kategori Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Kategoriler</Text>
        <TouchableOpacity style={styles.addHeaderButton} onPress={handleAddCategory}>
          <Text style={styles.addHeaderButtonText}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Tab Buttons */}
      {renderTabButtons()}

      {/* Category List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {currentCategories.length > 0 ? (
          currentCategories.map(renderCategoryItem)
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text.primary,
  },
  title: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  addHeaderButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary[500],
    borderRadius: 20,
  },
  addHeaderButtonText: {
    fontSize: 20,
    color: colors.text.inverse,
    fontWeight: 'bold',
  },

  // Tabs
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: spacing.cardRadius,
    backgroundColor: colors.background.secondary,
    alignItems: 'center',
  },
  tabButtonActive: {
    backgroundColor: colors.primary[500],
  },
  tabButtonText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    fontWeight: '600',
  },
  tabButtonTextActive: {
    color: colors.text.inverse,
  },

  // List
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.screenPadding,
    paddingBottom: spacing['4xl'],
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.surface.primary,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  categoryIconText: {
    fontSize: 18,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  categoryName: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  defaultBadge: {
    backgroundColor: colors.primary[100],
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: spacing.xs,
  },
  defaultBadgeText: {
    ...typography.styles.overline,
    color: colors.primary[700],
    fontSize: 10,
  },
  categoryStats: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  moreButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreButtonText: {
    fontSize: 20,
    color: colors.text.secondary,
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing['6xl'],
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: spacing.lg,
  },
  emptyStateTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 250,
    marginBottom: spacing.xl,
  },
  addButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: spacing.cardRadius,
  },
  addButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },
});

export default CategoriesScreen;
