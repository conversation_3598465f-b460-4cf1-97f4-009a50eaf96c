import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Modal,
  FlatList,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { NumberFormatter } from '../../utils/number';
import { categoryService } from '../../services/categoryService';
import { budgetService } from '../../services/budgetService';
import { Category } from '../../types/models';
import LoadingSpinner from '../../components/common/LoadingSpinner';

// CategorySeparator will be defined after modalStyles

// Color options for budget
const BUDGET_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
];

interface AddBudgetScreenProps {
  route?: {
    params?: {
      budgetId?: string;
    };
  };
}

const AddBudgetScreen: React.FC<AddBudgetScreenProps> = ({ route }) => {
  const { navigate, goBack } = useNavigation();
  const { state: authState } = useAuth();
  
  const isEditing = !!route?.params?.budgetId;
  
  // Form data
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    period: 'monthly' as 'monthly' | 'yearly',
    categoryId: '',
    color: '#10B981',
  });
  
  // UI states
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  
  // Data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      setIsLoadingData(true);
      
      if (authState.isAuthenticated && !authState.isGuest) {
        const categoriesData = await categoryService.getCategories();
        // Only expense categories for budgets
        setCategories(categoriesData?.filter(cat => cat.type === 'expense') || []);
      }
    } catch (error) {
      console.error('[AddBudgetScreen] Error fetching categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken hata oluştu');
    } finally {
      setIsLoadingData(false);
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  // Load data on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories, authState.isAuthenticated, authState.isGuest]);

  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Bütçe adı gerekli';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Tutar gerekli';
    } else if (!NumberFormatter.isValidNumber(formData.amount)) {
      newErrors.amount = 'Geçerli bir tutar girin';
    } else if (NumberFormatter.parseCurrency(formData.amount) <= 0) {
      newErrors.amount = 'Tutar sıfırdan büyük olmalı';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Kategori seçimi gerekli';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSave = useCallback(async () => {
    if (!validateForm()) return;

    // Check if user is in guest mode
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Bütçe eklemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => navigate('Login') },
        ]
      );
      return;
    }

    setIsLoading(true);

    try {
      const budgetData = {
        name: formData.name,
        amount: NumberFormatter.parseCurrency(formData.amount),
        category_id: formData.categoryId,
        period: formData.period,
        color: formData.color,
      };

      console.log('[AddBudgetScreen] Saving budget:', budgetData);

      // Call API
      if (isEditing) {
        // TODO: Implement update budget
        Alert.alert('Bilgi', 'Bütçe güncelleme özelliği yakında eklenecek');
      } else {
        await budgetService.createBudget(budgetData);
      }
      
      Alert.alert(
        'Başarılı',
        `Bütçe ${isEditing ? 'güncellendi' : 'eklendi'}.`,
        [{ text: 'Tamam', onPress: () => goBack() }]
      );
    } catch (error) {
      console.error('[AddBudgetScreen] Error saving budget:', error);
      Alert.alert(
        'Hata',
        error instanceof Error ? error.message : 'Bütçe kaydedilirken bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsLoading(false);
    }
  }, [authState.isGuest, validateForm, isEditing, formData, navigate, goBack]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleCategorySelect = (category: Category) => {
    setFormData(prev => ({ ...prev, categoryId: category.id || '' }));
    setErrors(prev => ({ ...prev, categoryId: '' }));
    setShowCategoryModal(false);
  };

  const selectedCategory = categories.find(cat => cat.id === formData.categoryId);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={goBack}>
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>
            <Text style={styles.title}>
              {isEditing ? 'Bütçeyi Düzenle' : 'Yeni Bütçe'}
            </Text>
            <View style={styles.headerRight} />
          </View>

          {/* Form */}
          <View style={styles.form}>
            {/* Name */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Bütçe Adı</Text>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                placeholder="Örn: Market Bütçesi"
                placeholderTextColor="#64748b"
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name}</Text>
              )}
            </View>

            {/* Amount */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Tutar</Text>
              <TextInput
                style={[styles.input, errors.amount && styles.inputError]}
                value={formData.amount}
                onChangeText={(value) => handleInputChange('amount', value)}
                placeholder="0,00"
                placeholderTextColor="#64748b"
                keyboardType="numeric"
              />
              {errors.amount && (
                <Text style={styles.errorText}>{errors.amount}</Text>
              )}
            </View>

            {/* Period */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Dönem</Text>
              <View style={styles.periodSelector}>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    formData.period === 'monthly' && styles.periodButtonActive
                  ]}
                  onPress={() => handleInputChange('period', 'monthly')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    formData.period === 'monthly' && styles.periodButtonTextActive
                  ]}>Aylık</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    formData.period === 'yearly' && styles.periodButtonActive
                  ]}
                  onPress={() => handleInputChange('period', 'yearly')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    formData.period === 'yearly' && styles.periodButtonTextActive
                  ]}>Yıllık</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Category */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Kategori</Text>
              <TouchableOpacity 
                style={[styles.selector, errors.categoryId && styles.inputError]}
                onPress={() => setShowCategoryModal(true)}
                disabled={isLoadingData || categories.length === 0}
              >
                {selectedCategory ? (
                  <View style={styles.selectedItem}>
                    <View style={[
                      styles.selectedCategoryIcon,
                      { backgroundColor: selectedCategory.color || '#0ea5e9' }
                    ]}>
                      <Text style={styles.selectedCategoryIconText}>{selectedCategory.icon || '📁'}</Text>
                    </View>
                    <Text style={styles.selectedItemText}>{selectedCategory.name}</Text>
                  </View>
                ) : (
                  <Text style={styles.selectorPlaceholder}>
                    {isLoadingData ? 'Yükleniyor...' : 
                     categories.length === 0 ? 'Kategori bulunamadı' : 
                     'Kategori seçin'}
                  </Text>
                )}
                <Text style={styles.selectorArrow}>›</Text>
              </TouchableOpacity>
              {errors.categoryId && (
                <Text style={styles.errorText}>{errors.categoryId}</Text>
              )}
            </View>
          </View>

          {/* Color Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Renk</Text>
            <View style={styles.colorGrid}>
              {BUDGET_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    formData.color === color && styles.colorOptionSelected
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, color }))}
                >
                  {formData.color === color && (
                    <Text style={styles.colorOptionCheck}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Save Button */}
          <TouchableOpacity 
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Kaydediliyor...' : isEditing ? 'Güncelle' : 'Kaydet'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Category Selection Modal */}
      <Modal
        visible={showCategoryModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <SafeAreaView style={modalStyles.container}>
          <View style={modalStyles.header}>
            <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
              <Text style={modalStyles.cancelButton}>İptal</Text>
            </TouchableOpacity>
            <Text style={modalStyles.title}>Kategori Seçin</Text>
            <View style={modalStyles.headerRight} />
          </View>
          
          {isLoadingData ? (
            <View style={modalStyles.loadingContainer}>
              <LoadingSpinner />
            </View>
          ) : (
            <FlatList
              data={categories}
              keyExtractor={(item) => item.id || ''}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    modalStyles.listItem,
                    formData.categoryId === item.id && modalStyles.listItemSelected
                  ]}
                  onPress={() => handleCategorySelect(item)}
                >
                  <View style={modalStyles.itemLeft}>
                    <View style={[
                      modalStyles.categoryIconCircle,
                      { backgroundColor: item.color || '#0ea5e9' }
                    ]}>
                      <Text style={modalStyles.categoryIconText}>{item.icon || '📁'}</Text>
                    </View>
                    <View style={modalStyles.categoryInfo}>
                      <Text style={modalStyles.categoryTitle}>{item.name}</Text>
                      <Text style={modalStyles.categorySubtitle}>Gider kategorisi</Text>
                    </View>
                  </View>
                  {formData.categoryId === item.id && (
                    <View style={modalStyles.selectedIndicator}>
                      <Text style={modalStyles.checkmark}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              ItemSeparatorComponent={CategorySeparator}
            />
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

// Main styles
const styles = {
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginBottom: 32,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  backButtonText: {
    fontSize: 24,
    color: '#1e293b',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    color: '#1e293b',
    textAlign: 'center' as const,
  },
  headerRight: {
    width: 40,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#1e293b',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1e293b',
    backgroundColor: '#ffffff',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    marginTop: 4,
  },
  periodSelector: {
    flexDirection: 'row' as const,
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center' as const,
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: '#ffffff',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#64748b',
  },
  periodButtonTextActive: {
    color: '#1e293b',
  },
  selector: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    backgroundColor: '#ffffff',
  },
  selectedItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  selectedCategoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 12,
  },
  selectedCategoryIconText: {
    fontSize: 16,
    color: '#ffffff',
  },
  selectedItemText: {
    fontSize: 16,
    color: '#1e293b',
  },
  selectorPlaceholder: {
    fontSize: 16,
    color: '#64748b',
  },
  selectorArrow: {
    fontSize: 18,
    color: '#64748b',
  },
  saveButton: {
    backgroundColor: '#0ea5e9',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center' as const,
    marginTop: 24,
  },
  saveButtonDisabled: {
    backgroundColor: '#94a3b8',
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#ffffff',
  },
  // Color picker styles
  colorGrid: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 12,
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    borderColor: '#1e293b',
    borderWidth: 3,
  },
  colorOptionCheck: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold' as const,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
};

// Modal styles
const modalStyles = {
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  cancelButton: {
    fontSize: 16,
    color: '#0ea5e9',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    color: '#1e293b',
  },
  headerRight: {
    width: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  listItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
  },
  listItemSelected: {
    backgroundColor: '#f0f9ff',
  },
  itemLeft: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  categoryIconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 16,
  },
  categoryIconText: {
    fontSize: 24,
    color: '#ffffff',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#1e293b',
    marginBottom: 2,
  },
  categorySubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  selectedIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#0ea5e9',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  checkmark: {
    fontSize: 18,
    color: '#ffffff',
    fontWeight: 'bold' as const,
  },
  separator: {
    height: 1,
    backgroundColor: '#f1f5f9',
    marginLeft: 20,
  },
};

// Separator component for category list
const CategorySeparator = () => <View style={modalStyles.separator} />;

export default AddBudgetScreen;
