import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '../../navigation/AppNavigator';
import { colors } from '../../theme/colors';

const BudgetScreen: React.FC = () => {
  const { state: authState } = useAuth();
  const { navigate } = useNavigation();

  // Guest state
  if (authState.isGuest) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.greeting}>Bütçelerim</Text>
          <Text style={styles.date}>Harcama takibi</Text>
        </View>

        <View style={styles.guestContainer}>
          <Text style={styles.guestIcon}>💰</Text>
          <Text style={styles.guestTitle}>Bütçelerinizi Görüntüle</Text>
          <Text style={styles.guestText}>
            <PERSON>ütçe takibi ve harcama analizleri için giriş yapmanız gerekiyor.
          </Text>
          <TouchableOpacity 
            style={styles.loginButton} 
            onPress={() => navigate('Login')}
          >
            <Text style={styles.loginButtonText}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Authenticated user - empty state for now
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>Bütçelerim</Text>
        <Text style={styles.date}>Harcama takibi</Text>
        <TouchableOpacity style={styles.addButton}>
          <Text style={styles.addButtonText}>+ Yeni Bütçe</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>💰</Text>
        <Text style={styles.emptyTitle}>Henüz bütçe yok</Text>
        <Text style={styles.emptyText}>
          İlk bütçenizi oluşturarak harcamalarınızı takip etmeye başlayın
        </Text>
        <TouchableOpacity style={styles.addButton}>
          <Text style={styles.addButtonText}>Bütçe Ekle</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.surface.primary,
    marginBottom: 4,
  },
  date: {
    fontSize: 16,
    color: colors.surface.primary,
    opacity: 0.9,
    marginBottom: 20,
  },
  addButton: {
    backgroundColor: colors.surface.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  addButtonText: {
    color: colors.primary[500],
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  guestTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  guestText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },
  loginButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  loginButtonText: {
    color: colors.surface.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  
  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },
});

export default BudgetScreen;
