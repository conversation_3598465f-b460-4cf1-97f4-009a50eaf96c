import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '../../navigation/AppNavigator';
import { budgetService, Budget } from '../../services/budgetService';
import { NumberFormatter } from '../../utils/number';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const BudgetScreen: React.FC = () => {
  const { state: authState } = useAuth();
  const { navigate } = useNavigation();

  // State
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch budgets from API
  const fetchBudgets = useCallback(async () => {
    try {
      setError(null);

      // Only fetch data if user is authenticated (not guest)
      if (authState.isAuthenticated && !authState.isGuest) {
        const budgetData = await budgetService.getBudgets();
        setBudgets(budgetData || []);
      } else {
        // For guest users, show empty state
        setBudgets([]);
      }
    } catch (fetchError) {
      console.error('[BudgetScreen] Error fetching budgets:', fetchError);
      setError(fetchError instanceof Error ? fetchError.message : 'Bütçeler yüklenirken hata oluştu');
      setBudgets([]);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  // Initial load
  useEffect(() => {
    fetchBudgets();
  }, [authState.isAuthenticated, authState.isGuest, fetchBudgets]);

  // Pull to refresh
  const onRefresh = () => {
    setIsRefreshing(true);
    fetchBudgets();
  };

  // Calculate progress percentage
  const calculateProgress = (spent: number, amount: number): number => {
    if (amount === 0) return 0;
    return Math.min((spent / amount) * 100, 100);
  };

  // Get progress color
  const getProgressColor = (progress: number): string => {
    if (progress >= 90) return '#ef4444'; // red
    if (progress >= 75) return '#f59e0b'; // yellow
    return '#22c55e'; // green
  };

  // Guest state
  if (authState.isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        {/* Header like ReportsScreen */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigate('Home')}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Bütçelerim</Text>
          <View style={styles.headerRight} />
        </View>

        <View style={styles.guestContainer}>
          <Text style={styles.guestIcon}>💰</Text>
          <Text style={styles.guestTitle}>Bütçelerinizi Görüntüle</Text>
          <Text style={styles.guestText}>
            Bütçe takibi ve harcama analizleri için giriş yapmanız gerekiyor.
          </Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigate('Login')}
          >
            <Text style={styles.loginButtonText}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigate('Home')}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Bütçelerim</Text>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.loadingContainer}>
          <LoadingSpinner />
        </View>
      </SafeAreaView>
    );
  }

  // Authenticated user with data
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigate('Home')}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Bütçelerim</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigate('AddBudget')}
        >
          <Text style={styles.addButtonText}>+ Ekle</Text>
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchBudgets}>
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={['#0ea5e9']}
            tintColor="#0ea5e9"
          />
        }
      >
        {budgets.length > 0 ? (
          <View style={styles.budgetList}>
            {budgets.map((budget) => {
              const progress = calculateProgress(budget.spent || 0, budget.amount);
              const progressColor = budget.color || getProgressColor(progress);
              const remaining = budget.amount - (budget.spent || 0);

              return (
                <View key={budget.id} style={styles.budgetItem}>
                  <View style={styles.budgetHeader}>
                    <View style={styles.budgetTitleRow}>
                      {budget.color && (
                        <View style={[styles.colorIndicator, { backgroundColor: budget.color }]} />
                      )}
                      <View style={styles.budgetTitleContainer}>
                        <Text style={styles.budgetName}>{budget.name}</Text>
                        <Text style={styles.budgetCategory}>{budget.categoryName || 'Kategori'}</Text>
                      </View>
                    </View>
                    <Text style={styles.budgetPeriod}>
                      {budget.period === 'monthly' ? 'Aylık' : 'Yıllık'}
                    </Text>
                  </View>

                  <View style={styles.budgetAmounts}>
                    <Text style={styles.budgetSpent}>
                      Harcanan: {NumberFormatter.formatCurrency(budget.spent || 0, budget.currency)}
                    </Text>
                    <Text style={styles.budgetTotal}>
                      / {NumberFormatter.formatCurrency(budget.amount, budget.currency)}
                    </Text>
                  </View>

                  <View style={styles.progressContainer}>
                    <View style={styles.progressBar}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            width: `${progress}%`,
                            backgroundColor: progressColor
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: progressColor }]}>
                      {progress.toFixed(1)}%
                    </Text>
                  </View>

                  <View style={styles.budgetFooter}>
                    <Text style={[
                      styles.remainingAmount,
                      remaining >= 0 ? styles.positiveAmount : styles.negativeAmount,
                      budget.color && { color: budget.color }
                    ]}>
                      Kalan: {NumberFormatter.formatCurrency(remaining, budget.currency)}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>💰</Text>
            <Text style={styles.emptyTitle}>Henüz bütçe yok</Text>
            <Text style={styles.emptyText}>
              İlk bütçenizi oluşturarak harcamalarınızı takip etmeye başlayın
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={() => navigate('AddBudget')}
            >
              <Text style={styles.emptyStateButtonText}>Bütçe Ekle</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  // Header like ReportsScreen
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: '#1e293b',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  headerRight: {
    width: 40,
  },
  addButton: {
    backgroundColor: '#0ea5e9',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },

  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  guestTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  guestText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },
  loginButton: {
    backgroundColor: '#0ea5e9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },

  // Empty State Button
  emptyStateButton: {
    backgroundColor: '#0ea5e9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Error
  errorContainer: {
    backgroundColor: '#fef2f2',
    margin: 20,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginBottom: 8,
  },
  retryButton: {
    alignSelf: 'flex-start',
  },
  retryButtonText: {
    color: '#ef4444',
    fontSize: 14,
    fontWeight: '600',
  },

  // ScrollView
  scrollView: {
    flex: 1,
  },

  // Budget List
  budgetList: {
    padding: 20,
  },
  budgetItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  budgetTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  budgetName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  budgetTitleContainer: {
    flex: 1,
  },
  budgetCategory: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 2,
  },
  budgetPeriod: {
    fontSize: 12,
    color: '#64748b',
    backgroundColor: '#f1f5f9',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  budgetAmounts: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  budgetSpent: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  budgetTotal: {
    fontSize: 16,
    color: '#64748b',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    marginRight: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    minWidth: 45,
    textAlign: 'right',
  },
  budgetFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  remainingAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  positiveAmount: {
    color: '#22c55e',
  },
  negativeAmount: {
    color: '#ef4444',
  },
});

export default BudgetScreen;