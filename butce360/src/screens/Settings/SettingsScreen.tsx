import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Switch,
  Alert,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
// import dataService from '../../services/dataService'; // Removed - not implemented

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  rightComponent,
}) => (
  <TouchableOpacity style={styles.settingItem} onPress={onPress} disabled={!onPress}>
    <View style={styles.settingItemLeft}>
      <Text style={styles.settingIcon}>{icon}</Text>
      <View style={styles.settingTextContainer}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    <View style={styles.settingItemRight}>
      {rightComponent}
      {showArrow && onPress && (
        <Text style={styles.settingArrow}>›</Text>
      )}
    </View>
  </TouchableOpacity>
);

const SettingsScreen: React.FC = () => {
  const { goBack } = useNavigation();

  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
  });

  const toggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const handleExportData = async () => {
    Alert.alert('Bilgi', 'Veri dışa aktarma özelliği yakında eklenecek.');
  };

  const handleImportData = async () => {
    Alert.alert('Bilgi', 'Veri içe aktarma özelliği yakında eklenecek.');
  };

  const handleResetApp = () => {
    Alert.alert(
      'Uygulamayı Sıfırla',
      'Bu işlem tüm verilerinizi silecektir. Bu işlem geri alınamaz!',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Sıfırla', style: 'destructive', onPress: () => {
          Alert.alert('Sıfırlandı', 'Uygulama başarıyla sıfırlandı');
        }}
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ayarlar</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>


        {/* Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bildirimler</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="🔔"
              title="Push Bildirimleri"
              subtitle="Harcama ve gelir bildirimleri"
              showArrow={false}
              rightComponent={
                <Switch
                  value={settings.notifications}
                  onValueChange={() => toggleSetting('notifications')}
                  trackColor={{ false: colors.border.primary, true: colors.primary[200] }}
                  thumbColor={settings.notifications ? colors.primary[500] : colors.surface.primary}
                />
              }
            />
          </View>
        </View>

        {/* Appearance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Görünüm</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="🌙"
              title="Karanlık Mod"
              subtitle="Gece modu aktif/pasif"
              showArrow={false}
              rightComponent={
                <Switch
                  value={settings.darkMode}
                  onValueChange={() => toggleSetting('darkMode')}
                  trackColor={{ false: colors.border.primary, true: colors.primary[200] }}
                  thumbColor={settings.darkMode ? colors.primary[500] : colors.surface.primary}
                />
              }
            />
            <SettingItem
              icon="🌍"
              title="Dil"
              subtitle="Türkçe"
              onPress={() => {
                Alert.alert('Yakında', 'Bu özellik yakında eklenecek');
              }}
            />
          </View>
        </View>

        {/* Data Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Veri Yönetimi</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="📤"
              title="Veri Dışa Aktar"
              subtitle="CSV formatında dışa aktar"
              onPress={handleExportData}
            />
            <SettingItem
              icon="📥"
              title="Veri İçe Aktar"
              subtitle="CSV dosyasından içe aktar"
              onPress={handleImportData}
            />
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tehlikeli İşlemler</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="🗑️"
              title="Uygulamayı Sıfırla"
              subtitle="Tüm verileri sil"
              onPress={handleResetApp}
            />
          </View>
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>Bütçe360 v1.0.0</Text>
          <Text style={styles.appInfoText}>© 2024 Nocy Tech</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  backButtonText: {
    fontSize: 24,
    color: colors.primary[500],
  },
  headerTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionContent: {
    backgroundColor: colors.surface.primary,
    marginHorizontal: spacing.lg,
    borderRadius: spacing.md,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '500',
  },
  settingSubtitle: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingArrow: {
    fontSize: 20,
    color: colors.text.tertiary,
    marginLeft: spacing.sm,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  appInfoText: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    marginBottom: spacing.xs,
  },
});

export default SettingsScreen;