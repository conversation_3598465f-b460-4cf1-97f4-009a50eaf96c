import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface TabItem {
  key: string;
  title: string;
  icon: string;
  screen: string;
}

const tabs: TabItem[] = [
  { key: 'home', title: 'Ana Sayfa', icon: '🏠', screen: 'Home' },
  { key: 'transactions', title: '<PERSON><PERSON><PERSON><PERSON>', icon: '💳', screen: 'Transactions' },
  { key: 'budget', title: 'Bütçe', icon: '🎯', screen: 'Budget' },
  { key: 'reports', title: 'Raporlar', icon: '📊', screen: 'Reports' },
  { key: 'profile', title: 'Profil', icon: '👤', screen: 'Profile' },
];

interface TabBarProps {
  activeTab: string;
}

const TabBar: React.FC<TabBarProps> = ({ activeTab }) => {
  const { navigate } = useNavigation();

  const handleTabPress = (tab: TabItem) => {
    navigate(tab.screen);
  };

  return (
    <View style={styles.container}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.key;
        
        return (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, isActive && styles.activeTab]}
            onPress={() => handleTabPress(tab)}
          >
            <Text style={[styles.icon, isActive && styles.activeIcon]}>
              {tab.icon}
            </Text>
            <Text style={[styles.title, isActive && styles.activeTitle]}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.surface.primary,
    borderTopWidth: 1,
    borderTopColor: colors.border.primary,
    paddingBottom: spacing.md,
    paddingTop: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    borderRadius: spacing.cardRadius,
  },
  activeTab: {
    backgroundColor: colors.primary[50],
  },
  icon: {
    fontSize: 20,
    marginBottom: spacing.xs,
  },
  activeIcon: {
    // Could add different styling for active icon
  },
  title: {
    ...typography.styles.tabLabel,
    color: colors.text.secondary,
  },
  activeTitle: {
    color: colors.primary[600],
    fontWeight: '600',
  },
});

export default TabBar;
