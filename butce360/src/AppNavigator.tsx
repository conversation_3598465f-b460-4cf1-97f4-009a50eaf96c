import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigation } from './navigation/AppNavigator';
import { useAuth } from './hooks/useAuth';

// Import screens
import WelcomePage from './screens/Welcome/WelcomePage';
import LoginPage from './screens/Auth/LoginPage';
import RegisterPage from './screens/Auth/RegisterPage';
import HomeScreen from './screens/Home/HomeScreen';
import AddTransactionScreen from './screens/Transactions/AddTransactionScreen';
// import TransactionListScreen from './screens/Transactions/TransactionListScreen';
import TransactionsScreen from './screens/Transactions/TransactionsScreen';
import BudgetScreen from './screens/Budget/BudgetScreen';
import AddBudgetScreen from './screens/Budget/AddBudgetScreen';
import CategoriesScreen from './screens/Categories/CategoriesScreen';
import AccountsScreen from './screens/Accounts/AccountsScreen';
import AddAccountScreen from './screens/Accounts/AddAccountScreen';
import ReportsScreen from './screens/Reports/ReportsScreen';
import CategoryReportScreen from './screens/Reports/CategoryReportScreen';
import MonthlyReportScreen from './screens/Reports/MonthlyReportScreen';
import ProfileScreen from './screens/Profile/ProfileScreen';
import EditProfileScreen from './screens/Profile/EditProfileScreen';
import ChangePasswordScreen from './screens/Profile/ChangePasswordScreen';
import AddCategoryScreen from './screens/Categories/AddCategoryScreen';
import SettingsScreen from './screens/Settings/SettingsScreen';
import HelpScreen from './screens/Help/HelpScreen';
import AboutScreen from './screens/About/AboutScreen';

// Import components
import TabBar from './components/common/TabBar';
// import LoadingSpinner from './components/common/LoadingSpinner';
import SplashScreen from './components/common/SplashScreen';

// Main App Navigator
const AppNavigator: React.FC = () => {
  const { navigationState } = useNavigation();
  const { state: authState } = useAuth();

  const renderScreen = () => {
    const { currentScreen, params } = navigationState;

    // Show splash screen while checking auth
    if (authState.isLoading) {
      return <SplashScreen />;
    }

    // Route to appropriate screen based on current screen
    switch (currentScreen) {
      case 'Welcome':
        return <WelcomePage />;

      case 'Login':
        return <LoginPage />;

      case 'Register':
        return <RegisterPage />;

      case 'MainTabs':
      case 'Home':
      case 'Transactions':
      case 'Budget':
      case 'Reports':
      case 'Profile':
        return renderMainApp();

      case 'AddTransaction':
      case 'EditTransaction':
        return <AddTransactionScreen route={{ params: params }} />;

      case 'AddBudget':
      case 'EditBudget':
        return <AddBudgetScreen route={{ params: params }} />;

      case 'Categories':
        return <CategoriesScreen />;

      case 'Accounts':
        return <AccountsScreen />;

      case 'About':
        return <AboutScreen />;

      case 'EditProfile':
        return <EditProfileScreen />;

      case 'Settings':
        return <SettingsScreen />;

      case 'Help':
        return <HelpScreen />;

      case 'ChangePassword':
        return <ChangePasswordScreen />;

      case 'AddCategory':
        return <AddCategoryScreen />;

      case 'AddAccount':
        return <AddAccountScreen />;

      case 'CategoryReport':
        return <CategoryReportScreen />;

      case 'MonthlyReport':
        return <MonthlyReportScreen />;

      // Add more screens as needed
      default:
        // Default to Welcome screen
        return <WelcomePage />;
    }
  };

  const renderMainApp = () => {
    const { currentScreen } = navigationState;

    // Check if user is authenticated (not guest) for protected screens
    if (!authState.isAuthenticated && !authState.isGuest) {
      // User is not authenticated, redirect to Welcome
      return <WelcomePage />;
    }

    // Determine current tab
    let currentTab = 'home';
    switch (currentScreen) {
      case 'Transactions':
        currentTab = 'transactions';
        break;
      case 'Budget':
        currentTab = 'budget';
        break;
      case 'Reports':
        currentTab = 'reports';
        break;
      case 'Profile':
        currentTab = 'profile';
        break;
      default:
        currentTab = 'home';
    }

    return (
      <View style={styles.mainApp}>
        <View style={styles.screenContainer}>
          {renderMainScreen()}
        </View>
        <TabBar activeTab={currentTab} />
      </View>
    );
  };

  const renderMainScreen = () => {
    const { currentScreen } = navigationState;

    switch (currentScreen) {
      case 'Transactions':
        return <TransactionsScreen />;
      case 'Budget':
        return <BudgetScreen />;
      case 'Reports':
        return <ReportsScreen />;
      case 'Profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen />;
    }
  };

  return (
    <View style={styles.container}>
      {renderScreen()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  mainApp: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
  },
});

export default AppNavigator;
