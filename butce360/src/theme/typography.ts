import { TextStyle } from 'react-native';

// Typography system for Butce360 app
export const typography = {
  // Font families
  fonts: {
    regular: 'System', // iOS: San Francisco, Android: Roboto
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
  },

  // Font sizes
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  // Line heights
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Font weights
  weights: {
    normal: '400' as TextStyle['fontWeight'],
    medium: '500' as TextStyle['fontWeight'],
    semibold: '600' as TextStyle['fontWeight'],
    bold: '700' as TextStyle['fontWeight'],
  },

  // Text styles
  styles: {
    // Headings
    h1: {
      fontSize: 36,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 43,
      letterSpacing: -0.5,
    } as TextStyle,

    h2: {
      fontSize: 30,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 36,
      letterSpacing: -0.25,
    } as TextStyle,

    h3: {
      fontSize: 24,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 29,
      letterSpacing: 0,
    } as TextStyle,

    h4: {
      fontSize: 20,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 24,
      letterSpacing: 0,
    } as TextStyle,

    h5: {
      fontSize: 18,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: 0,
    } as TextStyle,

    h6: {
      fontSize: 16,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 19,
      letterSpacing: 0,
    } as TextStyle,

    // Body text
    body1: {
      fontSize: 16,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: 0,
    } as TextStyle,

    body2: {
      fontSize: 14,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: 0,
    } as TextStyle,

    // Captions and labels
    caption: {
      fontSize: 12,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 16,
      letterSpacing: 0.4,
    } as TextStyle,

    label: {
      fontSize: 14,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 17,
      letterSpacing: 0.1,
    } as TextStyle,

    // Button text
    button: {
      fontSize: 14,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 19,
      letterSpacing: 0.5,
      textTransform: 'uppercase' as TextStyle['textTransform'],
    } as TextStyle,

    buttonSmall: {
      fontSize: 14,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 17,
      letterSpacing: 0.5,
      textTransform: 'uppercase' as TextStyle['textTransform'],
    } as TextStyle,

    // Currency and numbers
    currency: {
      fontSize: 24,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 29,
      letterSpacing: -0.25,
    } as TextStyle,

    currencyLarge: {
      fontSize: 36,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 43,
      letterSpacing: -0.5,
    } as TextStyle,

    currencySmall: {
      fontSize: 16,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 19,
      letterSpacing: 0,
    } as TextStyle,

    // Navigation
    tabLabel: {
      fontSize: 12,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 14,
      letterSpacing: 0.4,
    } as TextStyle,

    // Form elements
    input: {
      fontSize: 16,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: 0,
    } as TextStyle,

    inputLabel: {
      fontSize: 14,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 17,
      letterSpacing: 0.1,
    } as TextStyle,

    inputError: {
      fontSize: 12,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 16,
      letterSpacing: 0.4,
    } as TextStyle,

    // Special text styles
    overline: {
      fontSize: 10,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 12,
      letterSpacing: 1.5,
      textTransform: 'uppercase' as TextStyle['textTransform'],
    } as TextStyle,

    subtitle1: {
      fontSize: 16,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: 0.15,
    } as TextStyle,

    subtitle2: {
      fontSize: 14,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: 0.1,
    } as TextStyle,
  },
} as const;

// Utility function to get text style with color
export const getTextStyle = (
  style: keyof typeof typography.styles,
  color?: string
): TextStyle => {
  const baseStyle = typography.styles[style];
  return color ? { ...baseStyle, color } : baseStyle;
};