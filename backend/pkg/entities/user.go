package entities

type User struct {
	Base
	Username        string  `json:"username"`
	Email           string  `json:"email" gorm:"unique"`
	Password        string  `json:"password"`
	Name            string  `json:"name"`
	LastLogin       string  `json:"last_login"`
	Status          string  `json:"status"`                        // "active", "inactive", "banned"
	GoogleID        *string `json:"google_id" gorm:"unique"`       // Google OAuth ID
	AppleID         *string `json:"apple_id" gorm:"unique"`        // Apple OAuth ID
	GoogleAuth      *string `json:"google_auth" gorm:"type:jsonb"` // Google OAuth data
	AppleAuth       *string `json:"apple_auth" gorm:"type:jsonb"`  // Apple OAuth data
	OAuthProvider   *string `json:"oauth_provider"`                // "google", "apple", "email"
	ProfileImageURL *string `json:"profile_image_url"`             // Profile image from OAuth
}
